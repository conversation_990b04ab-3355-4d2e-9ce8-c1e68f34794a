import React, { useState, useEffect } from 'react';
import { View, StyleSheet, RefreshControl } from 'react-native';
import { Text, Card, DataTable, Searchbar, Chip, useTheme } from 'react-native-paper';
import { FlashList } from '@shopify/flash-list';
import { useQuery } from '@tanstack/react-query';

interface StockScore {
  symbol: string;
  money_flow_score: number;
  accumulation_score: number;
  momentum_score: number;
  sector_correlation_score: number;
  total_score: number;
  updated_at: string;
}

type SortField = 'total_score' | 'money_flow_score' | 'accumulation_score' | 'momentum_score';

interface SortConfig {
  field: SortField;
  direction: 'asc' | 'desc';
}

const API_URL = 'http://localhost:8000/api/v1';

const HomeScreen = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('total');
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: 'total_score', direction: 'desc' });
  const [page, setPage] = useState(0);
  const itemsPerPage = 10;

  const { data: stocks, isLoading, refetch } = useQuery<StockScore[]>({
    queryKey: ['stocks'],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/stocks/scores`);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    },
  });

  const filteredStocks = React.useMemo(() => {
    if (!stocks) return [];
    
    let result = stocks.filter(stock =>
      stock.symbol.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Sắp xếp
    result.sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      return sortConfig.direction === 'desc' ? bValue - aValue : aValue - bValue;
    });

    return result;
  }, [stocks, searchQuery, sortConfig]);

  const renderScoreCell = (score: number) => {
    let color = theme.colors.error;
    if (score >= 70) color = theme.colors.primary;
    else if (score >= 50) color = theme.colors.warning;

    return (
      <Text style={{ color, fontWeight: 'bold' }}>
        {score.toFixed(1)}
      </Text>
    );
  };

  const renderStockItem = ({ item }: { item: StockScore }) => (
    <DataTable.Row>
      <DataTable.Cell>
        <Text style={styles.symbol}>{item.symbol}</Text>
      </DataTable.Cell>
      <DataTable.Cell numeric>
        {renderScoreCell(item.total_score)}
      </DataTable.Cell>
      <DataTable.Cell numeric>
        {renderScoreCell(item.money_flow_score)}
      </DataTable.Cell>
      <DataTable.Cell numeric>
        {renderScoreCell(item.accumulation_score)}
      </DataTable.Cell>
      <DataTable.Cell numeric>
        {renderScoreCell(item.momentum_score)}
      </DataTable.Cell>
    </DataTable.Row>
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Tìm kiếm mã cổ phiếu"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />
      
      <View style={styles.filterContainer}>
        <Chip
          selected={selectedFilter === 'total'}
          onPress={() => setSelectedFilter('total')}
          style={styles.filterChip}
        >
          Tổng điểm
        </Chip>
        <Chip
          selected={selectedFilter === 'money_flow'}
          onPress={() => setSelectedFilter('money_flow')}
          style={styles.filterChip}
        >
          Dòng tiền
        </Chip>
        <Chip
          selected={selectedFilter === 'accumulation'}
          onPress={() => setSelectedFilter('accumulation')}
          style={styles.filterChip}
        >
          Tích lũy
        </Chip>
      </View>

      <View>
        <DataTable style={styles.dataTable}>
          <DataTable.Header>
            <DataTable.Title>Mã CP</DataTable.Title>
            <DataTable.Title numeric
              sortDirection={sortConfig.field === 'total_score' ? sortConfig.direction : undefined}
              onPress={() => setSortConfig(prev => ({
                field: 'total_score',
                direction: prev.field === 'total_score' && prev.direction === 'desc' ? 'asc' : 'desc'
              }))}
            >Tổng điểm</DataTable.Title>
            <DataTable.Title numeric
              sortDirection={sortConfig.field === 'money_flow_score' ? sortConfig.direction : undefined}
              onPress={() => setSortConfig(prev => ({
                field: 'money_flow_score',
                direction: prev.field === 'money_flow_score' && prev.direction === 'desc' ? 'asc' : 'desc'
              }))}
            >Dòng tiền</DataTable.Title>
            <DataTable.Title numeric
              sortDirection={sortConfig.field === 'accumulation_score' ? sortConfig.direction : undefined}
              onPress={() => setSortConfig(prev => ({
                field: 'accumulation_score',
                direction: prev.field === 'accumulation_score' && prev.direction === 'desc' ? 'asc' : 'desc'
              }))}
            >Tích lũy</DataTable.Title>
            <DataTable.Title numeric
              sortDirection={sortConfig.field === 'momentum_score' ? sortConfig.direction : undefined}
              onPress={() => setSortConfig(prev => ({
                field: 'momentum_score',
                direction: prev.field === 'momentum_score' && prev.direction === 'desc' ? 'asc' : 'desc'
              }))}
            >Momentum</DataTable.Title>
          </DataTable.Header>
        </DataTable>

        <FlashList
          data={filteredStocks.slice(page * itemsPerPage, (page + 1) * itemsPerPage)}
          renderItem={renderStockItem}
          estimatedItemSize={200}
          refreshControl={
            <RefreshControl refreshing={isLoading} onRefresh={refetch} />
          }
          contentContainerStyle={styles.listContent}
        />

        <DataTable.Pagination
          page={page}
          numberOfPages={Math.ceil((filteredStocks?.length || 0) / itemsPerPage)}
          onPageChange={setPage}
          label={`${page + 1} / ${Math.ceil((filteredStocks?.length || 0) / itemsPerPage)}`}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dataTable: {
    backgroundColor: theme.colors.surface,
    marginBottom: 8,
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    margin: 16,
    elevation: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  listContent: {
    padding: 16,
  },
  card: {
    marginBottom: 8,
  },
  symbol: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HomeScreen;