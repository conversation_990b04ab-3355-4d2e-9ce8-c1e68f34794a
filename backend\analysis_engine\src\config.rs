//! Configuration management for the analysis engine

use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub redis_url: String,
    pub rabbitmq_url: String,
    pub analysis_interval: u64,
    pub batch_size: usize,
    pub max_concurrent_analysis: usize,
    pub scoring_weights: ScoringWeights,
    pub technical_params: TechnicalParams,
    pub environment: String,
    pub log_level: String,
    pub metrics_port: u16,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ScoringWeights {
    pub money_flow: f64,
    pub accumulation: f64,
    pub momentum: f64,
    pub sector_correlation: f64,
    pub volume_profile: f64,
    pub volatility: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TechnicalParams {
    pub rsi_period: usize,
    pub macd_fast: usize,
    pub macd_slow: usize,
    pub macd_signal: usize,
    pub bollinger_period: usize,
    pub bollinger_std_dev: f64,
    pub volume_sma_period: usize,
    pub money_flow_period: usize,
    pub stochastic_k_period: usize,
    pub stochastic_d_period: usize,
}

impl Default for ScoringWeights {
    fn default() -> Self {
        Self {
            money_flow: 0.25,
            accumulation: 0.25,
            momentum: 0.20,
            sector_correlation: 0.15,
            volume_profile: 0.10,
            volatility: 0.05,
        }
    }
}

impl Default for TechnicalParams {
    fn default() -> Self {
        Self {
            rsi_period: 14,
            macd_fast: 12,
            macd_slow: 26,
            macd_signal: 9,
            bollinger_period: 20,
            bollinger_std_dev: 2.0,
            volume_sma_period: 20,
            money_flow_period: 14,
            stochastic_k_period: 14,
            stochastic_d_period: 3,
        }
    }
}

impl Config {
    /// Load configuration from environment variables
    pub fn from_env() -> Result<Self> {
        dotenvy::dotenv().ok(); // Load .env file if present
        
        let database_url = env::var("DATABASE_URL")
            .context("DATABASE_URL environment variable is required")?;
        
        let redis_url = env::var("REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".to_string());
        
        let rabbitmq_url = env::var("RABBITMQ_URL")
            .unwrap_or_else(|_| "amqp://localhost:5672".to_string());
        
        let analysis_interval = env::var("ANALYSIS_INTERVAL")
            .unwrap_or_else(|_| "60".to_string())
            .parse()
            .context("Invalid ANALYSIS_INTERVAL")?;
        
        let batch_size = env::var("BATCH_SIZE")
            .unwrap_or_else(|_| "100".to_string())
            .parse()
            .context("Invalid BATCH_SIZE")?;
        
        let max_concurrent_analysis = env::var("MAX_CONCURRENT_ANALYSIS")
            .unwrap_or_else(|_| "10".to_string())
            .parse()
            .context("Invalid MAX_CONCURRENT_ANALYSIS")?;
        
        let environment = env::var("ENVIRONMENT")
            .unwrap_or_else(|_| "development".to_string());
        
        let log_level = env::var("LOG_LEVEL")
            .unwrap_or_else(|_| "info".to_string());
        
        let metrics_port = env::var("METRICS_PORT")
            .unwrap_or_else(|_| "9090".to_string())
            .parse()
            .context("Invalid METRICS_PORT")?;
        
        Ok(Self {
            database_url,
            redis_url,
            rabbitmq_url,
            analysis_interval,
            batch_size,
            max_concurrent_analysis,
            scoring_weights: ScoringWeights::default(),
            technical_params: TechnicalParams::default(),
            environment,
            log_level,
            metrics_port,
        })
    }
    
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate scoring weights sum to 1.0
        let total_weight = self.scoring_weights.money_flow
            + self.scoring_weights.accumulation
            + self.scoring_weights.momentum
            + self.scoring_weights.sector_correlation
            + self.scoring_weights.volume_profile
            + self.scoring_weights.volatility;
        
        if (total_weight - 1.0).abs() > 0.01 {
            anyhow::bail!("Scoring weights must sum to 1.0, got {}", total_weight);
        }
        
        // Validate technical parameters
        if self.technical_params.rsi_period == 0 {
            anyhow::bail!("RSI period must be greater than 0");
        }
        
        if self.technical_params.macd_fast >= self.technical_params.macd_slow {
            anyhow::bail!("MACD fast period must be less than slow period");
        }
        
        if self.technical_params.bollinger_std_dev <= 0.0 {
            anyhow::bail!("Bollinger Bands standard deviation must be positive");
        }
        
        Ok(())
    }
    
    /// Check if running in production environment
    pub fn is_production(&self) -> bool {
        self.environment.to_lowercase() == "production"
    }
    
    /// Check if running in development environment
    pub fn is_development(&self) -> bool {
        self.environment.to_lowercase() == "development"
    }
}
