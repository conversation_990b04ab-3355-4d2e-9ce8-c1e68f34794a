version: '3.8'

services:
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    environment:
      - POSTGRES_USER=stockfilterx
      - POSTGRES_PASSWORD=stockfilterx
      - POSTGRES_DB=stockfilterx
    ports:
      - "5432:5432"
    volumes:
      - timescaledb_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  api_gateway:
    build:
      context: ./backend/api_gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*******************************************************/stockfilterx
      - REDIS_URL=redis://redis:6379
    depends_on:
      - timescaledb
      - redis

  analysis_engine:
    build:
      context: ./backend/analysis_engine
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=*******************************************************/stockfilterx
      - REDIS_URL=redis://redis:6379
    depends_on:
      - timescaledb
      - redis

  data_crawler:
    build:
      context: ./backend/data_crawler
      dockerfile: Dockerfile
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

volumes:
  timescaledb_data:
  redis_data: