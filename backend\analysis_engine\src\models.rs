//! Data models for the analysis engine

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StockData {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub volume: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StockScore {
    pub symbol: String,
    pub money_flow_score: f64,
    pub accumulation_score: f64,
    pub momentum_score: f64,
    pub sector_correlation_score: f64,
    pub volume_profile_score: f64,
    pub volatility_score: f64,
    pub total_score: f64,
    pub updated_at: DateTime<Utc>,
    pub confidence: f64,
    pub recommendation: Recommendation,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum Recommendation {
    StrongBuy,
    Buy,
    Hold,
    Sell,
    StrongSell,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TechnicalIndicators {
    pub rsi: Option<f64>,
    pub macd: Option<MacdValues>,
    pub bollinger_bands: Option<BollingerBands>,
    pub moving_averages: HashMap<String, f64>,
    pub stochastic: Option<StochasticValues>,
    pub volume_indicators: VolumeIndicators,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MacdValues {
    pub macd_line: f64,
    pub signal_line: f64,
    pub histogram: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BollingerBands {
    pub upper_band: f64,
    pub middle_band: f64,
    pub lower_band: f64,
    pub bandwidth: f64,
    pub percent_b: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StochasticValues {
    pub k_percent: f64,
    pub d_percent: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolumeIndicators {
    pub volume_sma: f64,
    pub volume_ratio: f64,
    pub money_flow_index: Option<f64>,
    pub on_balance_volume: f64,
    pub volume_weighted_average_price: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResult {
    pub symbol: String,
    pub timestamp: DateTime<Utc>,
    pub stock_data: Vec<StockData>,
    pub technical_indicators: TechnicalIndicators,
    pub score: StockScore,
    pub market_context: MarketContext,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketContext {
    pub market_trend: MarketTrend,
    pub sector_performance: f64,
    pub market_volatility: f64,
    pub trading_volume_trend: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketTrend {
    Bullish,
    Bearish,
    Sideways,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SectorData {
    pub sector: String,
    pub symbols: Vec<String>,
    pub average_score: f64,
    pub correlation_matrix: HashMap<String, HashMap<String, f64>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertTrigger {
    pub symbol: String,
    pub alert_type: AlertType,
    pub threshold: f64,
    pub current_value: f64,
    pub timestamp: DateTime<Utc>,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertType {
    PriceBreakout,
    VolumeSpike,
    ScoreChange,
    TechnicalSignal,
    SectorRotation,
}

impl StockData {
    /// Calculate typical price (HLC/3)
    pub fn typical_price(&self) -> f64 {
        (self.high + self.low + self.close) / 3.0
    }
    
    /// Calculate true range
    pub fn true_range(&self, previous_close: Option<f64>) -> f64 {
        let hl = self.high - self.low;
        
        if let Some(prev_close) = previous_close {
            let hc = (self.high - prev_close).abs();
            let lc = (self.low - prev_close).abs();
            hl.max(hc).max(lc)
        } else {
            hl
        }
    }
    
    /// Calculate price change percentage
    pub fn price_change_percent(&self, previous_close: f64) -> f64 {
        ((self.close - previous_close) / previous_close) * 100.0
    }
    
    /// Check if this is a doji candle
    pub fn is_doji(&self, threshold: f64) -> bool {
        let body_size = (self.close - self.open).abs();
        let total_range = self.high - self.low;
        
        if total_range == 0.0 {
            return true;
        }
        
        (body_size / total_range) < threshold
    }
    
    /// Check if this is a hammer candle
    pub fn is_hammer(&self) -> bool {
        let body_size = (self.close - self.open).abs();
        let lower_shadow = self.open.min(self.close) - self.low;
        let upper_shadow = self.high - self.open.max(self.close);
        
        lower_shadow > 2.0 * body_size && upper_shadow < body_size
    }
}

impl Recommendation {
    /// Convert score to recommendation
    pub fn from_score(score: f64) -> Self {
        match score {
            s if s >= 80.0 => Recommendation::StrongBuy,
            s if s >= 60.0 => Recommendation::Buy,
            s if s >= 40.0 => Recommendation::Hold,
            s if s >= 20.0 => Recommendation::Sell,
            _ => Recommendation::StrongSell,
        }
    }
    
    /// Get numeric value for recommendation
    pub fn to_numeric(&self) -> i32 {
        match self {
            Recommendation::StrongBuy => 2,
            Recommendation::Buy => 1,
            Recommendation::Hold => 0,
            Recommendation::Sell => -1,
            Recommendation::StrongSell => -2,
        }
    }
}

impl Default for TechnicalIndicators {
    fn default() -> Self {
        Self {
            rsi: None,
            macd: None,
            bollinger_bands: None,
            moving_averages: HashMap::new(),
            stochastic: None,
            volume_indicators: VolumeIndicators {
                volume_sma: 0.0,
                volume_ratio: 0.0,
                money_flow_index: None,
                on_balance_volume: 0.0,
                volume_weighted_average_price: 0.0,
            },
        }
    }
}

impl MarketTrend {
    /// Determine market trend from price data
    pub fn from_price_trend(trend_value: f64) -> Self {
        match trend_value {
            t if t > 0.02 => MarketTrend::Bullish,
            t if t < -0.02 => MarketTrend::Bearish,
            _ => MarketTrend::Sideways,
        }
    }
}
