from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..auth import get_current_active_user
from ..models import User, AlertSettings
from ..database import get_db

router = APIRouter(prefix="/alerts", tags=["alerts"])

@router.get("/settings", response_model=AlertSettings)
async def get_alert_settings(current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings:
        # Tạo cài đặt mặc định nếu chưa có
        settings = AlertSettings(user_id=current_user.id)
        db.add(settings)
        await db.commit()
        await db.refresh(settings)
    return settings

@router.put("/settings", response_model=AlertSettings)
async def update_alert_settings(settings_update: AlertSettings, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings:
        settings = AlertSettings(user_id=current_user.id)
        db.add(settings)
    
    settings.price_alerts = settings_update.price_alerts
    settings.score_alerts = settings_update.score_alerts
    settings.notification_channels = settings_update.notification_channels
    
    await db.commit()
    await db.refresh(settings)
    return settings

@router.post("/price/{symbol}")
async def create_price_alert(symbol: str, price: float, condition: str, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings:
        settings = AlertSettings(user_id=current_user.id)
        db.add(settings)
    
    if symbol not in settings.price_alerts:
        settings.price_alerts[symbol] = []
    
    settings.price_alerts[symbol].append({
        "price": price,
        "condition": condition,  # "above" hoặc "below"
        "active": True
    })
    
    await db.commit()
    return {"message": "Đã tạo cảnh báo giá thành công"}

@router.post("/score/{symbol}")
async def create_score_alert(symbol: str, score_type: str, threshold: float, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings:
        settings = AlertSettings(user_id=current_user.id)
        db.add(settings)
    
    if symbol not in settings.score_alerts:
        settings.score_alerts[symbol] = []
    
    settings.score_alerts[symbol].append({
        "score_type": score_type,  # total_score, money_flow_score, etc.
        "threshold": threshold,
        "active": True
    })
    
    await db.commit()
    return {"message": "Đã tạo cảnh báo điểm số thành công"}

@router.delete("/price/{symbol}/{alert_id}")
async def delete_price_alert(symbol: str, alert_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings or symbol not in settings.price_alerts or alert_id >= len(settings.price_alerts[symbol]):
        raise HTTPException(status_code=404, detail="Không tìm thấy cảnh báo")
    
    settings.price_alerts[symbol].pop(alert_id)
    if not settings.price_alerts[symbol]:
        del settings.price_alerts[symbol]
    
    await db.commit()
    return {"message": "Đã xóa cảnh báo giá thành công"}

@router.delete("/score/{symbol}/{alert_id}")
async def delete_score_alert(symbol: str, alert_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    settings = await db.query(AlertSettings).filter(AlertSettings.user_id == current_user.id).first()
    if not settings or symbol not in settings.score_alerts or alert_id >= len(settings.score_alerts[symbol]):
        raise HTTPException(status_code=404, detail="Không tìm thấy cảnh báo")
    
    settings.score_alerts[symbol].pop(alert_id)
    if not settings.score_alerts[symbol]:
        del settings.score_alerts[symbol]
    
    await db.commit()
    return {"message": "Đã xóa cảnh báo điểm số thành công"}