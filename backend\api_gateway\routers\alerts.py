"""
Alerts router for StockFilterX API
"""
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from auth import get_current_active_user
from models import (
    Alert, AlertCreate, AlertUpdate, AlertSettings,
    Notification, PaginatedResponse
)
from database import get_db
from db_models import UserDB, AlertDB, AlertSettingsDB, NotificationDB

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[Alert])
async def get_alerts(
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    symbol: Optional[str] = Query(None),
    alert_type: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """Get user's alerts"""
    try:
        query = select(AlertDB).where(AlertDB.user_id == current_user.id)

        if symbol:
            query = query.where(AlertDB.symbol == symbol.upper())
        if alert_type:
            query = query.where(AlertDB.alert_type == alert_type)
        if is_active is not None:
            query = query.where(AlertDB.is_active == is_active)

        query = query.offset(skip).limit(limit).order_by(AlertDB.created_at.desc())

        result = await db.execute(query)
        alerts = result.scalars().all()

        return [
            Alert(
                id=a.id,
                symbol=a.symbol,
                alert_type=a.alert_type,
                condition=a.condition,
                message=a.message,
                is_active=a.is_active,
                user_id=a.user_id,
                created_at=a.created_at,
                updated_at=a.updated_at,
                triggered_at=a.triggered_at,
                trigger_count=a.trigger_count
            )
            for a in alerts
        ]

    except Exception as e:
        logger.error(f"Error getting alerts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể lấy danh sách cảnh báo"
        )


@router.post("/", response_model=Alert)
async def create_alert(
    alert: AlertCreate,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new alert"""
    try:
        # Validate alert condition
        condition = alert.condition
        required_fields = ["field", "operator", "value"]
        if not all(field in condition for field in required_fields):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Điều kiện cảnh báo không hợp lệ"
            )

        # Create new alert
        db_alert = AlertDB(
            user_id=current_user.id,
            symbol=alert.symbol.upper(),
            alert_type=alert.alert_type,
            condition=alert.condition,
            message=alert.message,
            is_active=alert.is_active
        )

        db.add(db_alert)
        await db.commit()
        await db.refresh(db_alert)

        logger.info(f"Alert created: {alert.symbol} by user {current_user.username}")

        return Alert(
            id=db_alert.id,
            symbol=db_alert.symbol,
            alert_type=db_alert.alert_type,
            condition=db_alert.condition,
            message=db_alert.message,
            is_active=db_alert.is_active,
            user_id=db_alert.user_id,
            created_at=db_alert.created_at,
            updated_at=db_alert.updated_at,
            triggered_at=db_alert.triggered_at,
            trigger_count=db_alert.trigger_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating alert: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể tạo cảnh báo"
        )


@router.get("/{alert_id}", response_model=Alert)
async def get_alert(
    alert_id: int,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific alert"""
    try:
        result = await db.execute(
            select(AlertDB).where(
                and_(
                    AlertDB.id == alert_id,
                    AlertDB.user_id == current_user.id
                )
            )
        )
        alert = result.scalar_one_or_none()

        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Không tìm thấy cảnh báo"
            )

        return Alert(
            id=alert.id,
            symbol=alert.symbol,
            alert_type=alert.alert_type,
            condition=alert.condition,
            message=alert.message,
            is_active=alert.is_active,
            user_id=alert.user_id,
            created_at=alert.created_at,
            updated_at=alert.updated_at,
            triggered_at=alert.triggered_at,
            trigger_count=alert.trigger_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting alert {alert_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể lấy thông tin cảnh báo"
        )

@router.put("/{alert_id}", response_model=Alert)
async def update_alert(
    alert_id: int,
    alert_update: AlertUpdate,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update an alert"""
    try:
        result = await db.execute(
            select(AlertDB).where(
                and_(
                    AlertDB.id == alert_id,
                    AlertDB.user_id == current_user.id
                )
            )
        )
        alert = result.scalar_one_or_none()

        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Không tìm thấy cảnh báo"
            )

        # Update fields
        update_data = alert_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(alert, field, value)

        alert.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(alert)

        logger.info(f"Alert updated: {alert.symbol} by user {current_user.username}")

        return Alert(
            id=alert.id,
            symbol=alert.symbol,
            alert_type=alert.alert_type,
            condition=alert.condition,
            message=alert.message,
            is_active=alert.is_active,
            user_id=alert.user_id,
            created_at=alert.created_at,
            updated_at=alert.updated_at,
            triggered_at=alert.triggered_at,
            trigger_count=alert.trigger_count
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating alert {alert_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể cập nhật cảnh báo"
        )


@router.delete("/{alert_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_alert(
    alert_id: int,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete an alert"""
    try:
        result = await db.execute(
            select(AlertDB).where(
                and_(
                    AlertDB.id == alert_id,
                    AlertDB.user_id == current_user.id
                )
            )
        )
        alert = result.scalar_one_or_none()

        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Không tìm thấy cảnh báo"
            )

        await db.delete(alert)
        await db.commit()

        logger.info(f"Alert deleted: {alert.symbol} by user {current_user.username}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting alert {alert_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể xóa cảnh báo"
        )


@router.get("/settings", response_model=AlertSettings)
async def get_alert_settings(
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's alert settings"""
    try:
        result = await db.execute(
            select(AlertSettingsDB).where(AlertSettingsDB.user_id == current_user.id)
        )
        settings = result.scalar_one_or_none()

        if not settings:
            # Create default settings
            settings = AlertSettingsDB(
                user_id=current_user.id,
                notification_channels=["email"],
                email_notifications=True,
                push_notifications=True,
                sms_notifications=False
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)

        return AlertSettings(
            user_id=settings.user_id,
            notification_channels=settings.notification_channels,
            email_notifications=settings.email_notifications,
            push_notifications=settings.push_notifications,
            sms_notifications=settings.sms_notifications,
            webhook_url=settings.webhook_url,
            quiet_hours_start=settings.quiet_hours_start,
            quiet_hours_end=settings.quiet_hours_end
        )

    except Exception as e:
        logger.error(f"Error getting alert settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể lấy cài đặt cảnh báo"
        )


@router.put("/settings", response_model=AlertSettings)
async def update_alert_settings(
    settings_update: AlertSettings,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update user's alert settings"""
    try:
        result = await db.execute(
            select(AlertSettingsDB).where(AlertSettingsDB.user_id == current_user.id)
        )
        settings = result.scalar_one_or_none()

        if not settings:
            settings = AlertSettingsDB(user_id=current_user.id)
            db.add(settings)

        # Update fields
        settings.notification_channels = settings_update.notification_channels
        settings.email_notifications = settings_update.email_notifications
        settings.push_notifications = settings_update.push_notifications
        settings.sms_notifications = settings_update.sms_notifications
        settings.webhook_url = settings_update.webhook_url
        settings.quiet_hours_start = settings_update.quiet_hours_start
        settings.quiet_hours_end = settings_update.quiet_hours_end
        settings.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(settings)

        logger.info(f"Alert settings updated by user {current_user.username}")

        return AlertSettings(
            user_id=settings.user_id,
            notification_channels=settings.notification_channels,
            email_notifications=settings.email_notifications,
            push_notifications=settings.push_notifications,
            sms_notifications=settings.sms_notifications,
            webhook_url=settings.webhook_url,
            quiet_hours_start=settings.quiet_hours_start,
            quiet_hours_end=settings.quiet_hours_end
        )

    except Exception as e:
        logger.error(f"Error updating alert settings: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể cập nhật cài đặt cảnh báo"
        )