{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/store/*": ["store/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/constants/*": ["constants/*"], "@/hooks/*": ["hooks/*"]}, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": false}, "include": ["src/**/*", "App.tsx"], "exclude": ["node_modules"]}