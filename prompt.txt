Tôi đang xây dựng Bộ Lọc Cổ Phiếu cho thị trường Việt Nam.

Y<PERSON>u cầu:

1. <PERSON><PERSON><PERSON> triển thuật toán chấm điểm cho [dòng tiền/tích lũy/momentum/tương quan ngành]

2. Thiết kế API để lấy dữ liệu thị trường thời gian thực

3. Tối ưu giao diện người dùng cho ứng dụng mobile

4. Phát triển hệ thống cảnh báo và thông báo cho các mã đạt điểm cao

Công nghệ:

- Frontend: React/React Native

- Backend: Python (FastAPI/Flask), các thư viện phân tích kỹ thuật (TA-Lib)

- Database: MongoDB/TimescaleDB cho dữ liệu dạng chuỗi thời gian

- Cloud: AWS/GCP

Yêu cầu kỹ thuật:

- Thu thập dữ liệu từ các nguồn cấp dữ liệu chứng khoán có sẵn

- <PERSON><PERSON>h toán các chỉ báo kỹ thuật và chấm điểm theo thời gian thực

- API để frontend có thể truy vấn dữ liệu theo nhiều tiêu chí

- Đảm bảo hiệu suất cao khi xử lý dữ liệu lớn

Github readme with more good product names and nobody registered that website name worldwide, system architecture, workflow in mermaid syntax, project structure, advanced code for each file in the project (show exact file path). Code must be high performance and can handle many request simultaneously using Python or C++, Rust or Javascript (choose the best one). I prefer code using our own algorithms and free APIs. write commit message for each file so I can push to github.