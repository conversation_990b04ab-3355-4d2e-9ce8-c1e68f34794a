"""
SQLAlchemy database models for StockFilterX
"""
from datetime import datetime
from sqlalchemy import (
    Column, Integer, String, Float, Boolean, DateTime, Text, 
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base


class UserDB(Base):
    """User database model"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(100))
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(20), default="basic", nullable=False)
    subscription_status = Column(String(20), default="trial", nullable=False)
    disabled = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    subscription_expires_at = Column(DateTime(timezone=True))
    
    # Relationships
    watchlists = relationship("WatchlistDB", back_populates="user", cascade="all, delete-orphan")
    alerts = relationship("AlertDB", back_populates="user", cascade="all, delete-orphan")
    notifications = relationship("NotificationDB", back_populates="user", cascade="all, delete-orphan")
    alert_settings = relationship("AlertSettingsDB", back_populates="user", uselist=False)


class WatchlistDB(Base):
    """Watchlist database model"""
    __tablename__ = "watchlists"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(String(500))
    symbols = Column(JSON, default=list)  # List of stock symbols
    is_public = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("UserDB", back_populates="watchlists")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'name', name='unique_user_watchlist_name'),
        Index('idx_watchlist_user_id', 'user_id'),
    )


class AlertDB(Base):
    """Alert database model"""
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    symbol = Column(String(10), nullable=False, index=True)
    alert_type = Column(String(20), nullable=False)  # price, score, volume, technical
    condition = Column(JSON, nullable=False)  # {field, operator, value}
    message = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    triggered_at = Column(DateTime(timezone=True))
    trigger_count = Column(Integer, default=0)
    
    # Relationships
    user = relationship("UserDB", back_populates="alerts")
    
    # Indexes
    __table_args__ = (
        Index('idx_alert_user_symbol', 'user_id', 'symbol'),
        Index('idx_alert_active', 'is_active'),
    )


class AlertSettingsDB(Base):
    """Alert settings database model"""
    __tablename__ = "alert_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    notification_channels = Column(JSON, default=["email"])
    email_notifications = Column(Boolean, default=True)
    push_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    webhook_url = Column(String(500))
    quiet_hours_start = Column(String(5))  # HH:MM format
    quiet_hours_end = Column(String(5))    # HH:MM format
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("UserDB", back_populates="alert_settings")


class NotificationDB(Base):
    """Notification database model"""
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(String(50), nullable=False)
    channel = Column(String(20), nullable=False)  # email, push, sms, webhook
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True))
    sent_at = Column(DateTime(timezone=True))
    failed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    
    # Relationships
    user = relationship("UserDB", back_populates="notifications")
    
    # Indexes
    __table_args__ = (
        Index('idx_notification_user_created', 'user_id', 'created_at'),
        Index('idx_notification_type', 'notification_type'),
    )


class StockDataDB(Base):
    """Stock data database model (TimescaleDB hypertable)"""
    __tablename__ = "stock_data"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(10), nullable=False, index=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    
    # Indexes for time-series queries
    __table_args__ = (
        Index('idx_stock_data_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_stock_data_timestamp', 'timestamp'),
    )


class StockScoreDB(Base):
    """Stock score database model (TimescaleDB hypertable)"""
    __tablename__ = "stock_scores"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(10), nullable=False, index=True)
    money_flow_score = Column(Float, nullable=False)
    accumulation_score = Column(Float, nullable=False)
    momentum_score = Column(Float, nullable=False)
    sector_correlation_score = Column(Float, nullable=False)
    total_score = Column(Float, nullable=False)
    updated_at = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Indexes for efficient queries
    __table_args__ = (
        Index('idx_stock_score_symbol_updated', 'symbol', 'updated_at'),
        Index('idx_stock_score_total_score', 'total_score'),
        Index('idx_stock_score_updated', 'updated_at'),
    )


class UserSessionDB(Base):
    """User session database model for tracking active sessions"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    is_active = Column(Boolean, default=True)
    
    # Indexes
    __table_args__ = (
        Index('idx_session_user_active', 'user_id', 'is_active'),
        Index('idx_session_expires', 'expires_at'),
    )


class APIUsageDB(Base):
    """API usage tracking for rate limiting and analytics"""
    __tablename__ = "api_usage"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    endpoint = Column(String(200), nullable=False)
    method = Column(String(10), nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    response_time = Column(Float)  # in milliseconds
    status_code = Column(Integer)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Indexes for analytics and rate limiting
    __table_args__ = (
        Index('idx_api_usage_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_api_usage_endpoint', 'endpoint'),
        Index('idx_api_usage_timestamp', 'timestamp'),
    )
