from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from ..auth import get_current_active_user
from ..models import User, Watchlist, WatchlistCreate
from ..database import get_db

router = APIRouter(prefix="/watchlists", tags=["watchlists"])

@router.get("/", response_model=List[Watchlist])
async def get_watchlists(current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    return await db.query(Watchlist).filter(Watchlist.user_id == current_user.id).all()

@router.post("/", response_model=Watchlist)
async def create_watchlist(watchlist: WatchlistCreate, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    db_watchlist = Watchlist(
        **watchlist.dict(),
        user_id=current_user.id
    )
    db.add(db_watchlist)
    await db.commit()
    await db.refresh(db_watchlist)
    return db_watchlist

@router.get("/{watchlist_id}", response_model=Watchlist)
async def get_watchlist(watchlist_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    return watchlist

@router.put("/{watchlist_id}", response_model=Watchlist)
async def update_watchlist(watchlist_id: int, watchlist_update: WatchlistCreate, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    
    for key, value in watchlist_update.dict().items():
        setattr(watchlist, key, value)
    
    await db.commit()
    await db.refresh(watchlist)
    return watchlist

@router.delete("/{watchlist_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_watchlist(watchlist_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    
    await db.delete(watchlist)
    await db.commit()