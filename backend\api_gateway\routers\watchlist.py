"""
Watchlist router for StockFilterX API
"""
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from auth import get_current_active_user
from models import Watchlist, WatchlistCreate, WatchlistUpdate, PaginatedResponse
from database import get_db
from db_models import UserDB, WatchlistDB

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[Watchlist])
async def get_watchlists(
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """Get user's watchlists"""
    try:
        result = await db.execute(
            select(WatchlistDB)
            .where(WatchlistDB.user_id == current_user.id)
            .offset(skip)
            .limit(limit)
            .order_by(WatchlistDB.created_at.desc())
        )
        watchlists = result.scalars().all()

        return [
            Watchlist(
                id=w.id,
                name=w.name,
                description=w.description,
                symbols=w.symbols or [],
                user_id=w.user_id,
                created_at=w.created_at,
                updated_at=w.updated_at,
                is_public=w.is_public
            )
            for w in watchlists
        ]

    except Exception as e:
        logger.error(f"Error getting watchlists: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể lấy danh sách watchlist"
        )


@router.post("/", response_model=Watchlist)
async def create_watchlist(
    watchlist: WatchlistCreate,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new watchlist"""
    try:
        # Check if watchlist name already exists for this user
        result = await db.execute(
            select(WatchlistDB).where(
                and_(
                    WatchlistDB.user_id == current_user.id,
                    WatchlistDB.name == watchlist.name
                )
            )
        )

        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tên watchlist đã tồn tại"
            )

        # Create new watchlist
        db_watchlist = WatchlistDB(
            name=watchlist.name,
            description=watchlist.description,
            symbols=watchlist.symbols,
            user_id=current_user.id,
            is_public=False
        )

        db.add(db_watchlist)
        await db.commit()
        await db.refresh(db_watchlist)

        logger.info(f"Watchlist created: {watchlist.name} by user {current_user.username}")

        return Watchlist(
            id=db_watchlist.id,
            name=db_watchlist.name,
            description=db_watchlist.description,
            symbols=db_watchlist.symbols or [],
            user_id=db_watchlist.user_id,
            created_at=db_watchlist.created_at,
            updated_at=db_watchlist.updated_at,
            is_public=db_watchlist.is_public
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating watchlist: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Không thể tạo watchlist"
        )

@router.get("/{watchlist_id}", response_model=Watchlist)
async def get_watchlist(watchlist_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    return watchlist

@router.put("/{watchlist_id}", response_model=Watchlist)
async def update_watchlist(watchlist_id: int, watchlist_update: WatchlistCreate, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    
    for key, value in watchlist_update.dict().items():
        setattr(watchlist, key, value)
    
    await db.commit()
    await db.refresh(watchlist)
    return watchlist

@router.delete("/{watchlist_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_watchlist(watchlist_id: int, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    watchlist = await db.query(Watchlist).filter(
        Watchlist.id == watchlist_id,
        Watchlist.user_id == current_user.id
    ).first()
    if not watchlist:
        raise HTTPException(status_code=404, detail="Không tìm thấy watchlist")
    
    await db.delete(watchlist)
    await db.commit()