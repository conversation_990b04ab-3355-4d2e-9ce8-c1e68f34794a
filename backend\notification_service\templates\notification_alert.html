<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .alert-title {
            font-size: 18px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .alert-message {
            color: #856404;
        }
        .data-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .data-item {
            margin: 5px 0;
        }
        .data-label {
            font-weight: bold;
            color: #495057;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">StockFilterX</div>
            <p>Hệ thống cảnh báo thông minh</p>
        </div>
        
        <div class="alert-box">
            <div class="alert-title">{{ title }}</div>
            <div class="alert-message">{{ message }}</div>
        </div>
        
        {% if data %}
        <div class="data-section">
            <h3>Chi tiết cảnh báo:</h3>
            {% for key, value in data.items() %}
            <div class="data-item">
                <span class="data-label">{{ key }}:</span> {{ value }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div style="text-align: center;">
            <a href="https://stockfilterx.com/dashboard" class="button">Xem Dashboard</a>
        </div>
        
        <div class="footer">
            <p>Thời gian: {{ timestamp }}</p>
            <p>Bạn nhận được email này vì đã đăng ký cảnh báo từ StockFilterX.</p>
            <p>Để hủy đăng ký, vui lòng truy cập cài đặt tài khoản.</p>
        </div>
    </div>
</body>
</html>
