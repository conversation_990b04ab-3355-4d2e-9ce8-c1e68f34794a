# StockFilterX - Bộ Lọc Cổ Phiếu Thông Minh

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

StockFilterX là một nền tảng phân tích và lọc cổ phiếu thời gian thực cho thị trường chứng khoán Việt Nam. Hệ thống cung cấp các công cụ phân tích kỹ thuật tiên tiến, chấm điểm tự động và cảnh báo thông minh giúp nhà đầu tư đưa ra quyết định tốt hơn.

## Tính năng chính

- 🔍 Phân tích và chấm điểm theo nhiều tiêu chí:
  - Dòng tiền thông minh
  - Tích lũy/phân phối
  - Momentum
  - Tương quan ngành
- ⚡ Dữ liệu thị trường thời gian thực
- 📱 <PERSON>iao di<PERSON>n mobile thân thiện
- 🔔 <PERSON><PERSON> thống cảnh báo thông minh

## Kiến trú<PERSON> hệ thống

```mermaid
flowchart TB
    subgraph Data Collection
        DC[Data Crawler] --> MQ[Message Queue]
        MQ --> DP[Data Processor]
    end
    
    subgraph Analysis Engine
        DP --> TS[(TimescaleDB)]
        TS --> AE[Analysis Engine]
        AE --> CS[Scoring Service]
    end
    
    subgraph API Layer
        CS --> API[FastAPI Gateway]
        TS --> API
    end
    
    subgraph Frontend
        API --> WEB[Web UI]
        API --> MOB[Mobile App]
    end
    
    subgraph Notification
        CS --> NS[Notification Service]
        NS --> NC[Notification Channel]
    end
```

## Công nghệ sử dụng

- **Frontend**: React Native
- **Backend**: 
  - API Gateway: FastAPI
  - Analysis Engine: Rust
  - Data Crawler: Python
- **Database**: TimescaleDB
- **Message Queue**: Redis
- **Infrastructure**: AWS

## Cài đặt và phát triển

### Yêu cầu hệ thống

- Python 3.9+
- Rust 1.70+
- Node.js 18+
- Docker & Docker Compose

### Cài đặt

1. Clone repository:
```bash
git clone https://github.com/HectorTa1989/stockfilterx.git
cd stockfilterx
```

2. Cài đặt dependencies:
```bash
# Backend
cd backend
pip install -r requirements.txt

# Frontend
cd ../frontend
npm install
```

3. Khởi động services với Docker:
```bash
docker-compose up -d
```

4. Chạy ứng dụng:
```bash
# Backend API
cd backend
uvicorn main:app --reload

# Frontend Dev Server
cd ../frontend
npm run dev
```

## Đóng góp

Chúng tôi luôn chào đón mọi đóng góp từ cộng đồng. Vui lòng đọc [CONTRIBUTING.md](CONTRIBUTING.md) để biết thêm chi tiết.

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. Xem file [LICENSE](LICENSE) để biết thêm chi tiết.

## Liên hệ

- Website: https://stockfilterx.com
- Email: <EMAIL>
- Twitter: [@StockFilterX](https://twitter.com/StockFilterX)