# Database Configuration
DATABASE_URL=postgresql://stockfilterx:stockfilterx@localhost:5432/stockfilterx

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# Push Notification Configuration
FCM_SERVER_KEY=your-fcm-server-key

# Application Configuration
PORT=8001
ENVIRONMENT=development

# Logging
LOG_LEVEL=INFO
