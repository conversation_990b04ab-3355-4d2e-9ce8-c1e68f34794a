"""
StockFilterX Notification Service
Real-time notification service with WebSocket support and alert management
"""
import asyncio
import json
import logging
import os
import smtplib
from datetime import datetime
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum

import aiohttp
import redis.asyncio as redis
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
import asyncpg
from jinja2 import Environment, FileSystemLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class NotificationChannel(str, Enum):
    EMAIL = "email"
    WEBSOCKET = "websocket"
    PUSH = "push"
    SMS = "sms"
    WEBHOOK = "webhook"


class NotificationType(str, Enum):
    ALERT = "alert"
    SCORE_UPDATE = "score_update"
    SYSTEM = "system"
    MARKET_NEWS = "market_news"


@dataclass
class NotificationMessage:
    id: str
    user_id: int
    title: str
    message: str
    notification_type: NotificationType
    channel: NotificationChannel
    data: Optional[Dict] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()


class NotificationRequest(BaseModel):
    user_id: int
    title: str
    message: str
    notification_type: NotificationType
    channels: List[NotificationChannel]
    data: Optional[Dict] = None


class WebSocketManager:
    """Manages WebSocket connections for real-time notifications"""
    
    def __init__(self):
        self.active_connections: Dict[int, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: int):
        """Connect a user's WebSocket"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
        logger.info(f"WebSocket connected for user {user_id}")
    
    def disconnect(self, websocket: WebSocket, user_id: int):
        """Disconnect a user's WebSocket"""
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def send_personal_message(self, message: str, user_id: int):
        """Send message to a specific user"""
        if user_id in self.active_connections:
            disconnected = set()
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                self.active_connections[user_id].discard(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast message to all connected users"""
        for user_id, websockets in self.active_connections.items():
            disconnected = set()
            
            for websocket in websockets:
                try:
                    await websocket.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to user {user_id}: {e}")
                    disconnected.add(websocket)
            
            # Remove disconnected websockets
            for websocket in disconnected:
                websockets.discard(websocket)


class EmailService:
    """Email notification service"""
    
    def __init__(self):
        self.smtp_server = os.getenv("SMTP_SERVER", "localhost")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME", "")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")
        self.from_email = os.getenv("FROM_EMAIL", "<EMAIL>")
        
        # Setup Jinja2 for email templates
        self.template_env = Environment(
            loader=FileSystemLoader("templates")
        )
    
    async def send_email(self, to_email: str, subject: str, message: str, template_name: str = None, template_data: Dict = None):
        """Send email notification"""
        try:
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email
            
            # Create text content
            text_part = MimeText(message, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # Create HTML content if template is provided
            if template_name and template_data:
                try:
                    template = self.template_env.get_template(f"{template_name}.html")
                    html_content = template.render(**template_data)
                    html_part = MimeText(html_content, 'html', 'utf-8')
                    msg.attach(html_part)
                except Exception as e:
                    logger.warning(f"Failed to render email template: {e}")
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_username and self.smtp_password:
                    server.starttls()
                    server.login(self.smtp_username, self.smtp_password)
                
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False


class PushNotificationService:
    """Push notification service for mobile apps"""
    
    def __init__(self):
        self.fcm_server_key = os.getenv("FCM_SERVER_KEY", "")
        self.fcm_url = "https://fcm.googleapis.com/fcm/send"
    
    async def send_push_notification(self, device_token: str, title: str, body: str, data: Dict = None):
        """Send push notification via FCM"""
        if not self.fcm_server_key:
            logger.warning("FCM server key not configured")
            return False
        
        try:
            headers = {
                'Authorization': f'key={self.fcm_server_key}',
                'Content-Type': 'application/json',
            }
            
            payload = {
                'to': device_token,
                'notification': {
                    'title': title,
                    'body': body,
                },
                'data': data or {}
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.fcm_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Push notification sent successfully to {device_token}")
                        return True
                    else:
                        logger.error(f"Failed to send push notification: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending push notification: {e}")
            return False


class WebhookService:
    """Webhook notification service"""
    
    async def send_webhook(self, webhook_url: str, data: Dict):
        """Send webhook notification"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'StockFilterX-Notification-Service/1.0'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, headers=headers, json=data, timeout=30) as response:
                    if response.status == 200:
                        logger.info(f"Webhook sent successfully to {webhook_url}")
                        return True
                    else:
                        logger.error(f"Webhook failed with status {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending webhook to {webhook_url}: {e}")
            return False


class NotificationService:
    """Main notification service orchestrator"""
    
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.email_service = EmailService()
        self.push_service = PushNotificationService()
        self.webhook_service = WebhookService()
        
        # Database and Redis connections
        self.db_pool = None
        self.redis_client = None
    
    async def initialize(self):
        """Initialize database and Redis connections"""
        # Database connection
        database_url = os.getenv("DATABASE_URL", "postgresql://stockfilterx:stockfilterx@localhost:5432/stockfilterx")
        self.db_pool = await asyncpg.create_pool(database_url, min_size=5, max_size=20)
        
        # Redis connection
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_client = redis.from_url(redis_url)
        
        logger.info("Notification service initialized")
    
    async def send_notification(self, notification: NotificationMessage):
        """Send notification through specified channel"""
        try:
            success = False
            
            if notification.channel == NotificationChannel.WEBSOCKET:
                success = await self._send_websocket_notification(notification)
            elif notification.channel == NotificationChannel.EMAIL:
                success = await self._send_email_notification(notification)
            elif notification.channel == NotificationChannel.PUSH:
                success = await self._send_push_notification(notification)
            elif notification.channel == NotificationChannel.WEBHOOK:
                success = await self._send_webhook_notification(notification)
            
            # Save notification to database
            await self._save_notification_to_db(notification, success)
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return False
    
    async def _send_websocket_notification(self, notification: NotificationMessage) -> bool:
        """Send WebSocket notification"""
        try:
            message_data = {
                'id': notification.id,
                'type': notification.notification_type.value,
                'title': notification.title,
                'message': notification.message,
                'data': notification.data,
                'timestamp': notification.created_at.isoformat()
            }
            
            await self.websocket_manager.send_personal_message(
                json.dumps(message_data),
                notification.user_id
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")
            return False
    
    async def _send_email_notification(self, notification: NotificationMessage) -> bool:
        """Send email notification"""
        try:
            # Get user email from database
            async with self.db_pool.acquire() as conn:
                user_email = await conn.fetchval(
                    "SELECT email FROM users WHERE id = $1",
                    notification.user_id
                )
            
            if not user_email:
                logger.error(f"No email found for user {notification.user_id}")
                return False
            
            # Determine template based on notification type
            template_name = f"notification_{notification.notification_type.value}"
            template_data = {
                'title': notification.title,
                'message': notification.message,
                'data': notification.data or {},
                'timestamp': notification.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return await self.email_service.send_email(
                user_email,
                notification.title,
                notification.message,
                template_name,
                template_data
            )
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
            return False

    async def _send_push_notification(self, notification: NotificationMessage) -> bool:
        """Send push notification"""
        try:
            # Get user's device tokens from database
            async with self.db_pool.acquire() as conn:
                device_tokens = await conn.fetch(
                    "SELECT device_token FROM user_devices WHERE user_id = $1 AND is_active = true",
                    notification.user_id
                )

            if not device_tokens:
                logger.warning(f"No active device tokens for user {notification.user_id}")
                return False

            success_count = 0
            for row in device_tokens:
                device_token = row['device_token']
                success = await self.push_service.send_push_notification(
                    device_token,
                    notification.title,
                    notification.message,
                    notification.data
                )
                if success:
                    success_count += 1

            return success_count > 0

        except Exception as e:
            logger.error(f"Error sending push notification: {e}")
            return False

    async def _send_webhook_notification(self, notification: NotificationMessage) -> bool:
        """Send webhook notification"""
        try:
            # Get user's webhook URL from alert settings
            async with self.db_pool.acquire() as conn:
                webhook_url = await conn.fetchval(
                    "SELECT webhook_url FROM alert_settings WHERE user_id = $1",
                    notification.user_id
                )

            if not webhook_url:
                logger.warning(f"No webhook URL configured for user {notification.user_id}")
                return False

            webhook_data = {
                'id': notification.id,
                'user_id': notification.user_id,
                'type': notification.notification_type.value,
                'title': notification.title,
                'message': notification.message,
                'data': notification.data,
                'timestamp': notification.created_at.isoformat()
            }

            return await self.webhook_service.send_webhook(webhook_url, webhook_data)

        except Exception as e:
            logger.error(f"Error sending webhook notification: {e}")
            return False

    async def _save_notification_to_db(self, notification: NotificationMessage, success: bool):
        """Save notification to database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO notifications (
                        user_id, title, message, notification_type, channel,
                        created_at, sent_at, failed_at, error_message
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                    notification.user_id,
                    notification.title,
                    notification.message,
                    notification.notification_type.value,
                    notification.channel.value,
                    notification.created_at,
                    notification.created_at if success else None,
                    None if success else notification.created_at,
                    None if success else "Failed to send notification"
                )

        except Exception as e:
            logger.error(f"Error saving notification to database: {e}")

    async def process_message_queue(self):
        """Process notifications from message queue"""
        try:
            # Subscribe to notification channels
            pubsub = self.redis_client.pubsub()
            await pubsub.subscribe('notifications', 'alerts')

            logger.info("Started processing message queue")

            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        await self._process_notification_message(data)
                    except Exception as e:
                        logger.error(f"Error processing message: {e}")

        except Exception as e:
            logger.error(f"Error in message queue processing: {e}")

    async def _process_notification_message(self, data: Dict):
        """Process individual notification message"""
        try:
            # Create notification from message data
            notification = NotificationMessage(
                id=data.get('id', str(datetime.utcnow().timestamp())),
                user_id=data['user_id'],
                title=data['title'],
                message=data['message'],
                notification_type=NotificationType(data['notification_type']),
                channel=NotificationChannel(data['channel']),
                data=data.get('data'),
                created_at=datetime.fromisoformat(data.get('created_at', datetime.utcnow().isoformat()))
            )

            # Send notification
            await self.send_notification(notification)

        except Exception as e:
            logger.error(f"Error processing notification message: {e}")

    async def send_bulk_notification(self, request: NotificationRequest):
        """Send notification to multiple channels"""
        results = {}

        for channel in request.channels:
            notification = NotificationMessage(
                id=str(datetime.utcnow().timestamp()),
                user_id=request.user_id,
                title=request.title,
                message=request.message,
                notification_type=request.notification_type,
                channel=channel,
                data=request.data
            )

            success = await self.send_notification(notification)
            results[channel.value] = success

        return results


# FastAPI application
app = FastAPI(
    title="StockFilterX Notification Service",
    description="Real-time notification service with WebSocket support",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global notification service instance
notification_service = NotificationService()


@app.on_event("startup")
async def startup_event():
    """Initialize notification service on startup"""
    await notification_service.initialize()

    # Start message queue processing in background
    asyncio.create_task(notification_service.process_message_queue())


@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: int):
    """WebSocket endpoint for real-time notifications"""
    await notification_service.websocket_manager.connect(websocket, user_id)

    try:
        while True:
            # Keep connection alive and handle ping/pong
            data = await websocket.receive_text()

            # Echo back for ping/pong
            if data == "ping":
                await websocket.send_text("pong")

    except WebSocketDisconnect:
        notification_service.websocket_manager.disconnect(websocket, user_id)


@app.post("/api/v1/notifications/send")
async def send_notification_endpoint(request: NotificationRequest):
    """Send notification to specified channels"""
    try:
        results = await notification_service.send_bulk_notification(request)

        return {
            "success": True,
            "message": "Notifications sent",
            "results": results
        }

    except Exception as e:
        logger.error(f"Error in send notification endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/notifications/{user_id}")
async def get_user_notifications(user_id: int, limit: int = 50, offset: int = 0):
    """Get user's notifications"""
    try:
        async with notification_service.db_pool.acquire() as conn:
            notifications = await conn.fetch(
                """
                SELECT id, title, message, notification_type, channel,
                       created_at, read_at, sent_at, failed_at
                FROM notifications
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT $2 OFFSET $3
                """,
                user_id, limit, offset
            )

        return {
            "success": True,
            "notifications": [dict(row) for row in notifications]
        }

    except Exception as e:
        logger.error(f"Error getting user notifications: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/api/v1/notifications/{notification_id}/read")
async def mark_notification_read(notification_id: int):
    """Mark notification as read"""
    try:
        async with notification_service.db_pool.acquire() as conn:
            await conn.execute(
                "UPDATE notifications SET read_at = $1 WHERE id = $2",
                datetime.utcnow(), notification_id
            )

        return {"success": True, "message": "Notification marked as read"}

    except Exception as e:
        logger.error(f"Error marking notification as read: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        async with notification_service.db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")

        # Check Redis connection
        await notification_service.redis_client.ping()

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "database": "healthy",
                "redis": "healthy"
            }
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("PORT", "8001"))
    uvicorn.run(app, host="0.0.0.0", port=port)
