import asyncio
import aiohttp
import json
import redis
import logging
from datetime import datetime
from typing import Dict, List, Optional
from abc import ABC, abstractmethod

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarketDataSource(ABC):
    """Abstract class định nghĩa interface cho các nguồn dữ liệu thị trường"""
    
    @abstractmethod
    async def get_stock_list(self) -> List[str]:
        """Lấy danh sách mã cổ phiếu"""
        pass

    @abstractmethod
    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        """Lấy dữ liệu realtime cho danh sách mã"""
        pass

    @abstractmethod
    async def get_historical_data(self, symbol: str, from_date: str, to_date: str) -> List[Dict]:
        """Lấy dữ liệu lịch sử của một mã"""
        pass

class FireantDataSource(MarketDataSource):
    """Implement crawl dữ liệu từ Fireant API"""

    def __init__(self):
        self.base_url = "https://restv2.fireant.vn"
        self.session = None

    async def _init_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def get_stock_list(self) -> List[str]:
        await self._init_session()
        try:
            async with self.session.get(f"{self.base_url}/symbols") as response:
                if response.status == 200:
                    data = await response.json()
                    return [item['symbol'] for item in data if item.get('symbol')]
                else:
                    logger.error(f"Failed to get stock list: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting stock list: {str(e)}")
            return []

    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        await self._init_session()
        symbols_str = ",".join(symbols)
        try:
            async with self.session.get(
                f"{self.base_url}/market/quote",
                params={"symbols": symbols_str}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get realtime data: {response.status}")
                    return {}
        except Exception as e:
            logger.error(f"Error getting realtime data: {str(e)}")
            return {}

    async def get_historical_data(
        self,
        symbol: str,
        from_date: str,
        to_date: str
    ) -> List[Dict]:
        await self._init_session()
        try:
            async with self.session.get(
                f"{self.base_url}/symbols/{symbol}/historical-quotes",
                params={
                    "startDate": from_date,
                    "endDate": to_date
                }
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to get historical data: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting historical data: {str(e)}")
            return []

class CafeF_DataSource(MarketDataSource):
    """Implement crawl dữ liệu từ CafeF"""
    
    def __init__(self):
        self.base_url = "https://cafef.vn"
        self.session = None

    # TODO: Implement các phương thức crawl dữ liệu từ CafeF

class DataCrawler:
    """Class chính để quản lý việc thu thập dữ liệu"""

    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.data_sources = [
            FireantDataSource(),
            # CafeF_DataSource(),  # TODO: Implement và thêm vào
        ]

    async def _publish_market_data(self, data: Dict):
        """Publish dữ liệu thị trường vào Redis pub/sub"""
        try:
            self.redis_client.publish(
                'market_data',
                json.dumps({
                    'timestamp': datetime.now().isoformat(),
                    'data': data
                })
            )
        except Exception as e:
            logger.error(f"Error publishing market data: {str(e)}")

    async def crawl_market_data(self):
        """Hàm chính để crawl dữ liệu thị trường"""
        while True:
            try:
                # Lấy danh sách mã từ nguồn đầu tiên
                symbols = await self.data_sources[0].get_stock_list()
                if not symbols:
                    logger.warning("No symbols found")
                    continue

                # Chia thành các nhóm nhỏ để tránh quá tải
                chunk_size = 50
                symbol_chunks = [symbols[i:i + chunk_size] 
                               for i in range(0, len(symbols), chunk_size)]

                for chunk in symbol_chunks:
                    # Thu thập dữ liệu từ tất cả các nguồn
                    for source in self.data_sources:
                        data = await source.get_realtime_data(chunk)
                        if data:
                            await self._publish_market_data(data)

                # Delay giữa các lần crawl
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Error in crawl_market_data: {str(e)}")
                await asyncio.sleep(5)

async def main():
    redis_url = "redis://localhost:6379"
    crawler = DataCrawler(redis_url)
    await crawler.crawl_market_data()

if __name__ == "__main__":
    asyncio.run(main())