"""
Enhanced data crawler for StockFilterX with rate limiting and error handling
"""
import asyncio
import aiohttp
import json
import redis
import logging
import time
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import backoff
from aiohttp import ClientTimeout, ClientSession
from contextlib import asynccontextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """Data source types"""
    FIREANT = "fireant"
    CAFEF = "cafef"
    VNDIRECT = "vndirect"
    SSI = "ssi"


@dataclass
class CrawlerConfig:
    """Configuration for data crawler"""
    redis_url: str = "redis://localhost:6379"
    max_concurrent_requests: int = 10
    request_timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    chunk_size: int = 50
    crawl_interval: int = 1  # seconds
    enable_historical_data: bool = True
    historical_days: int = 30

    @classmethod
    def from_env(cls) -> 'CrawlerConfig':
        """Create config from environment variables"""
        return cls(
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            max_concurrent_requests=int(os.getenv("MAX_CONCURRENT_REQUESTS", "10")),
            request_timeout=int(os.getenv("REQUEST_TIMEOUT", "30")),
            retry_attempts=int(os.getenv("RETRY_ATTEMPTS", "3")),
            retry_delay=float(os.getenv("RETRY_DELAY", "1.0")),
            rate_limit_requests=int(os.getenv("RATE_LIMIT_REQUESTS", "100")),
            rate_limit_window=int(os.getenv("RATE_LIMIT_WINDOW", "60")),
            chunk_size=int(os.getenv("CHUNK_SIZE", "50")),
            crawl_interval=int(os.getenv("CRAWL_INTERVAL", "1")),
            enable_historical_data=os.getenv("ENABLE_HISTORICAL_DATA", "true").lower() == "true",
            historical_days=int(os.getenv("HISTORICAL_DAYS", "30"))
        )


class RateLimiter:
    """Rate limiter for API requests"""

    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = []

    async def acquire(self):
        """Acquire permission to make a request"""
        now = time.time()

        # Remove old requests outside the window
        self.requests = [req_time for req_time in self.requests
                        if now - req_time < self.window_seconds]

        # Check if we can make a request
        if len(self.requests) >= self.max_requests:
            # Calculate wait time
            oldest_request = min(self.requests)
            wait_time = self.window_seconds - (now - oldest_request)
            if wait_time > 0:
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                return await self.acquire()

        # Record this request
        self.requests.append(now)
        return True

class MarketDataSource(ABC):
    """Abstract class for market data sources"""

    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.rate_limiter = RateLimiter(
            config.rate_limit_requests,
            config.rate_limit_window
        )
        self.session: Optional[ClientSession] = None
        self._request_count = 0
        self._error_count = 0

    @asynccontextmanager
    async def get_session(self):
        """Get HTTP session with proper configuration"""
        if not self.session:
            timeout = ClientTimeout(total=self.config.request_timeout)
            connector = aiohttp.TCPConnector(
                limit=self.config.max_concurrent_requests,
                limit_per_host=self.config.max_concurrent_requests
            )
            self.session = ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    'User-Agent': 'StockFilterX/1.0 (Data Crawler)',
                    'Accept': 'application/json',
                    'Accept-Encoding': 'gzip, deflate'
                }
            )

        try:
            yield self.session
        finally:
            pass  # Keep session alive for reuse

    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None

    @backoff.on_exception(
        backoff.expo,
        (aiohttp.ClientError, asyncio.TimeoutError),
        max_tries=3,
        max_time=30
    )
    async def make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make HTTP request with rate limiting and retry logic"""
        await self.rate_limiter.acquire()

        try:
            async with self.get_session() as session:
                self._request_count += 1
                logger.debug(f"Making request to {url} with params {params}")

                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.debug(f"Successfully fetched data from {url}")
                        return data
                    else:
                        logger.warning(f"HTTP {response.status} from {url}")
                        self._error_count += 1
                        return None

        except Exception as e:
            logger.error(f"Request failed for {url}: {str(e)}")
            self._error_count += 1
            raise

    def get_stats(self) -> Dict[str, int]:
        """Get request statistics"""
        return {
            "requests": self._request_count,
            "errors": self._error_count,
            "success_rate": (self._request_count - self._error_count) / max(self._request_count, 1) * 100
        }

    @abstractmethod
    async def get_stock_list(self) -> List[str]:
        """Get list of stock symbols"""
        pass

    @abstractmethod
    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        """Get realtime data for symbols"""
        pass

    @abstractmethod
    async def get_historical_data(self, symbol: str, from_date: str, to_date: str) -> List[Dict]:
        """Get historical data for a symbol"""
        pass

    @abstractmethod
    def get_source_type(self) -> DataSourceType:
        """Get data source type"""
        pass

class FireantDataSource(MarketDataSource):
    """Enhanced Fireant API data source"""

    def __init__(self, config: CrawlerConfig):
        super().__init__(config)
        self.base_url = "https://restv2.fireant.vn"

    def get_source_type(self) -> DataSourceType:
        return DataSourceType.FIREANT

    async def get_stock_list(self) -> List[str]:
        """Get list of stock symbols from Fireant"""
        try:
            data = await self.make_request(f"{self.base_url}/symbols")
            if data and isinstance(data, list):
                symbols = [item.get('symbol') for item in data if item.get('symbol')]
                logger.info(f"Retrieved {len(symbols)} symbols from Fireant")
                return symbols
            return []
        except Exception as e:
            logger.error(f"Error getting stock list from Fireant: {str(e)}")
            return []

    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        """Get realtime data for symbols from Fireant"""
        if not symbols:
            return {}

        try:
            symbols_str = ",".join(symbols)
            data = await self.make_request(
                f"{self.base_url}/market/quote",
                params={"symbols": symbols_str}
            )

            if data:
                # Normalize data format
                normalized_data = {}
                for item in data if isinstance(data, list) else [data]:
                    if 'symbol' in item:
                        normalized_data[item['symbol']] = {
                            'symbol': item.get('symbol'),
                            'price': item.get('price', 0),
                            'change': item.get('change', 0),
                            'change_percent': item.get('changePercent', 0),
                            'volume': item.get('volume', 0),
                            'high': item.get('high', 0),
                            'low': item.get('low', 0),
                            'open': item.get('open', 0),
                            'timestamp': datetime.now().isoformat()
                        }

                logger.debug(f"Retrieved realtime data for {len(normalized_data)} symbols from Fireant")
                return normalized_data

            return {}

        except Exception as e:
            logger.error(f"Error getting realtime data from Fireant: {str(e)}")
            return {}

    async def get_historical_data(self, symbol: str, from_date: str, to_date: str) -> List[Dict]:
        """Get historical data for a symbol from Fireant"""
        try:
            data = await self.make_request(
                f"{self.base_url}/symbols/{symbol}/historical-quotes",
                params={
                    "startDate": from_date,
                    "endDate": to_date
                }
            )

            if data and isinstance(data, list):
                # Normalize historical data format
                normalized_data = []
                for item in data:
                    normalized_data.append({
                        'symbol': symbol,
                        'date': item.get('date'),
                        'open': item.get('open', 0),
                        'high': item.get('high', 0),
                        'low': item.get('low', 0),
                        'close': item.get('close', 0),
                        'volume': item.get('volume', 0),
                        'timestamp': item.get('date')
                    })

                logger.debug(f"Retrieved {len(normalized_data)} historical records for {symbol} from Fireant")
                return normalized_data

            return []

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from Fireant: {str(e)}")
            return []

class CafeFDataSource(MarketDataSource):
    """CafeF data source implementation"""

    def __init__(self, config: CrawlerConfig):
        super().__init__(config)
        self.base_url = "https://s.cafef.vn/Ajax"

    def get_source_type(self) -> DataSourceType:
        return DataSourceType.CAFEF

    async def get_stock_list(self) -> List[str]:
        """Get stock list from CafeF"""
        try:
            # CafeF doesn't have a direct API for stock list
            # Using common Vietnamese stock symbols
            vn_stocks = [
                "VIC", "VHM", "VNM", "BID", "CTG", "TCB", "HPG", "MSN", "SAB", "GAS",
                "VCB", "VJC", "PLX", "POW", "VRE", "MWG", "FPT", "SSI", "HDB", "TPB",
                "VPB", "STB", "ACB", "MBB", "EIB", "SHB", "VIB", "LPB", "OCB", "NVL"
            ]
            logger.info(f"Using predefined stock list for CafeF: {len(vn_stocks)} symbols")
            return vn_stocks
        except Exception as e:
            logger.error(f"Error getting stock list from CafeF: {str(e)}")
            return []

    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        """Get realtime data from CafeF"""
        if not symbols:
            return {}

        try:
            # CafeF API endpoint for stock quotes
            normalized_data = {}

            # Process symbols in smaller chunks to avoid overwhelming the API
            for symbol in symbols[:10]:  # Limit to avoid rate limiting
                try:
                    data = await self.make_request(
                        f"{self.base_url}/PageNew/DataHistory/PriceHistory.ashx",
                        params={
                            "Symbol": symbol,
                            "StartDate": "",
                            "EndDate": "",
                            "PageIndex": "1",
                            "PageSize": "1"
                        }
                    )

                    if data and 'Data' in data and data['Data']:
                        item = data['Data'][0]
                        normalized_data[symbol] = {
                            'symbol': symbol,
                            'price': float(item.get('ClosePrice', 0)),
                            'change': float(item.get('Change', 0)),
                            'change_percent': float(item.get('ChangePercent', 0)),
                            'volume': int(item.get('Volume', 0)),
                            'high': float(item.get('HighPrice', 0)),
                            'low': float(item.get('LowPrice', 0)),
                            'open': float(item.get('OpenPrice', 0)),
                            'timestamp': datetime.now().isoformat()
                        }

                except Exception as e:
                    logger.warning(f"Failed to get data for {symbol} from CafeF: {str(e)}")
                    continue

            logger.debug(f"Retrieved realtime data for {len(normalized_data)} symbols from CafeF")
            return normalized_data

        except Exception as e:
            logger.error(f"Error getting realtime data from CafeF: {str(e)}")
            return {}

    async def get_historical_data(self, symbol: str, from_date: str, to_date: str) -> List[Dict]:
        """Get historical data from CafeF"""
        try:
            data = await self.make_request(
                f"{self.base_url}/PageNew/DataHistory/PriceHistory.ashx",
                params={
                    "Symbol": symbol,
                    "StartDate": from_date,
                    "EndDate": to_date,
                    "PageIndex": "1",
                    "PageSize": "1000"
                }
            )

            if data and 'Data' in data and data['Data']:
                normalized_data = []
                for item in data['Data']:
                    normalized_data.append({
                        'symbol': symbol,
                        'date': item.get('TradingDate'),
                        'open': float(item.get('OpenPrice', 0)),
                        'high': float(item.get('HighPrice', 0)),
                        'low': float(item.get('LowPrice', 0)),
                        'close': float(item.get('ClosePrice', 0)),
                        'volume': int(item.get('Volume', 0)),
                        'timestamp': item.get('TradingDate')
                    })

                logger.debug(f"Retrieved {len(normalized_data)} historical records for {symbol} from CafeF")
                return normalized_data

            return []

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from CafeF: {str(e)}")
            return []


class VNDirectDataSource(MarketDataSource):
    """VNDirect data source implementation"""

    def __init__(self, config: CrawlerConfig):
        super().__init__(config)
        self.base_url = "https://finfo-api.vndirect.com.vn"

    def get_source_type(self) -> DataSourceType:
        return DataSourceType.VNDIRECT

    async def get_stock_list(self) -> List[str]:
        """Get stock list from VNDirect"""
        try:
            data = await self.make_request(f"{self.base_url}/v4/stocks")
            if data and 'data' in data:
                symbols = [item.get('code') for item in data['data'] if item.get('code')]
                logger.info(f"Retrieved {len(symbols)} symbols from VNDirect")
                return symbols
            return []
        except Exception as e:
            logger.error(f"Error getting stock list from VNDirect: {str(e)}")
            return []

    async def get_realtime_data(self, symbols: List[str]) -> Dict:
        """Get realtime data from VNDirect"""
        if not symbols:
            return {}

        try:
            symbols_str = ",".join(symbols)
            data = await self.make_request(
                f"{self.base_url}/v4/stocks/quote",
                params={"q": f"code:{symbols_str}"}
            )

            if data and 'data' in data:
                normalized_data = {}
                for item in data['data']:
                    symbol = item.get('code')
                    if symbol:
                        normalized_data[symbol] = {
                            'symbol': symbol,
                            'price': float(item.get('lastPrice', 0)),
                            'change': float(item.get('change', 0)),
                            'change_percent': float(item.get('pctChange', 0)),
                            'volume': int(item.get('totalVol', 0)),
                            'high': float(item.get('highPrice', 0)),
                            'low': float(item.get('lowPrice', 0)),
                            'open': float(item.get('openPrice', 0)),
                            'timestamp': datetime.now().isoformat()
                        }

                logger.debug(f"Retrieved realtime data for {len(normalized_data)} symbols from VNDirect")
                return normalized_data

            return {}

        except Exception as e:
            logger.error(f"Error getting realtime data from VNDirect: {str(e)}")
            return {}

    async def get_historical_data(self, symbol: str, from_date: str, to_date: str) -> List[Dict]:
        """Get historical data from VNDirect"""
        try:
            data = await self.make_request(
                f"{self.base_url}/v4/stocks/historical",
                params={
                    "q": f"code:{symbol}",
                    "fromDate": from_date,
                    "toDate": to_date
                }
            )

            if data and 'data' in data:
                normalized_data = []
                for item in data['data']:
                    normalized_data.append({
                        'symbol': symbol,
                        'date': item.get('date'),
                        'open': float(item.get('open', 0)),
                        'high': float(item.get('high', 0)),
                        'low': float(item.get('low', 0)),
                        'close': float(item.get('close', 0)),
                        'volume': int(item.get('volume', 0)),
                        'timestamp': item.get('date')
                    })

                logger.debug(f"Retrieved {len(normalized_data)} historical records for {symbol} from VNDirect")
                return normalized_data

            return []

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol} from VNDirect: {str(e)}")
            return []

class DataCrawler:
    """Enhanced data crawler with multiple sources and error handling"""

    def __init__(self, config: Optional[CrawlerConfig] = None):
        self.config = config or CrawlerConfig.from_env()
        self.redis_client = redis.from_url(self.config.redis_url)

        # Initialize data sources
        self.data_sources = [
            FireantDataSource(self.config),
            CafeFDataSource(self.config),
            VNDirectDataSource(self.config),
        ]

        self.is_running = False
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'symbols_processed': 0,
            'start_time': None
        }

    async def _publish_market_data(self, data: Dict, source_type: DataSourceType):
        """Publish market data to Redis pub/sub"""
        try:
            message = {
                'timestamp': datetime.now().isoformat(),
                'source': source_type.value,
                'data': data,
                'count': len(data)
            }

            # Publish to general market data channel
            self.redis_client.publish('market_data', json.dumps(message))

            # Publish to source-specific channel
            self.redis_client.publish(f'market_data_{source_type.value}', json.dumps(message))

            # Store latest data in Redis with expiration
            for symbol, symbol_data in data.items():
                key = f"stock_data:{symbol}:latest"
                self.redis_client.setex(key, 300, json.dumps(symbol_data))  # 5 minutes TTL

            logger.debug(f"Published {len(data)} symbols from {source_type.value}")

        except Exception as e:
            logger.error(f"Error publishing market data from {source_type.value}: {str(e)}")

    async def _publish_historical_data(self, symbol: str, data: List[Dict], source_type: DataSourceType):
        """Publish historical data to Redis"""
        try:
            message = {
                'timestamp': datetime.now().isoformat(),
                'source': source_type.value,
                'symbol': symbol,
                'data': data,
                'count': len(data)
            }

            self.redis_client.publish('historical_data', json.dumps(message))

            # Store historical data with longer TTL
            key = f"stock_data:{symbol}:historical"
            self.redis_client.setex(key, 3600, json.dumps(data))  # 1 hour TTL

            logger.debug(f"Published {len(data)} historical records for {symbol} from {source_type.value}")

        except Exception as e:
            logger.error(f"Error publishing historical data for {symbol}: {str(e)}")

    async def crawl_realtime_data(self):
        """Crawl realtime market data from all sources"""
        try:
            # Get symbols from the first available source
            symbols = []
            for source in self.data_sources:
                try:
                    symbols = await source.get_stock_list()
                    if symbols:
                        logger.info(f"Got {len(symbols)} symbols from {source.get_source_type().value}")
                        break
                except Exception as e:
                    logger.warning(f"Failed to get symbols from {source.get_source_type().value}: {str(e)}")
                    continue

            if not symbols:
                logger.warning("No symbols found from any source")
                return

            # Process symbols in chunks
            symbol_chunks = [symbols[i:i + self.config.chunk_size]
                           for i in range(0, len(symbols), self.config.chunk_size)]

            # Crawl data from all sources concurrently
            tasks = []
            for source in self.data_sources:
                for chunk in symbol_chunks:
                    task = self._crawl_chunk_from_source(source, chunk)
                    tasks.append(task)

            # Execute tasks with limited concurrency
            semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)

            async def limited_task(task):
                async with semaphore:
                    return await task

            results = await asyncio.gather(*[limited_task(task) for task in tasks], return_exceptions=True)

            # Update statistics
            successful = sum(1 for r in results if not isinstance(r, Exception))
            failed = len(results) - successful

            self.stats['successful_requests'] += successful
            self.stats['failed_requests'] += failed
            self.stats['total_requests'] += len(results)
            self.stats['symbols_processed'] += len(symbols)

            logger.info(f"Crawl completed: {successful} successful, {failed} failed")

        except Exception as e:
            logger.error(f"Error in crawl_realtime_data: {str(e)}")

    async def _crawl_chunk_from_source(self, source: MarketDataSource, symbols: List[str]):
        """Crawl data for a chunk of symbols from a specific source"""
        try:
            data = await source.get_realtime_data(symbols)
            if data:
                await self._publish_market_data(data, source.get_source_type())
                return len(data)
            return 0
        except Exception as e:
            logger.error(f"Error crawling chunk from {source.get_source_type().value}: {str(e)}")
            raise

    async def crawl_historical_data(self, symbols: Optional[List[str]] = None):
        """Crawl historical data for symbols"""
        if not self.config.enable_historical_data:
            logger.info("Historical data crawling is disabled")
            return

        try:
            if not symbols:
                # Get symbols from first available source
                for source in self.data_sources:
                    try:
                        symbols = await source.get_stock_list()
                        if symbols:
                            break
                    except Exception:
                        continue

            if not symbols:
                logger.warning("No symbols available for historical data crawling")
                return

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.historical_days)

            from_date = start_date.strftime("%Y-%m-%d")
            to_date = end_date.strftime("%Y-%m-%d")

            logger.info(f"Crawling historical data for {len(symbols)} symbols from {from_date} to {to_date}")

            # Process symbols with limited concurrency
            semaphore = asyncio.Semaphore(5)  # Lower concurrency for historical data

            async def crawl_symbol_historical(symbol: str):
                async with semaphore:
                    for source in self.data_sources:
                        try:
                            data = await source.get_historical_data(symbol, from_date, to_date)
                            if data:
                                await self._publish_historical_data(symbol, data, source.get_source_type())
                                return len(data)
                        except Exception as e:
                            logger.warning(f"Failed to get historical data for {symbol} from {source.get_source_type().value}: {str(e)}")
                            continue
                    return 0

            tasks = [crawl_symbol_historical(symbol) for symbol in symbols[:50]]  # Limit to avoid overwhelming
            results = await asyncio.gather(*tasks, return_exceptions=True)

            successful = sum(1 for r in results if isinstance(r, int) and r > 0)
            logger.info(f"Historical data crawl completed: {successful}/{len(tasks)} symbols processed")

        except Exception as e:
            logger.error(f"Error in crawl_historical_data: {str(e)}")

    async def run_continuous_crawling(self):
        """Run continuous data crawling"""
        self.is_running = True
        self.stats['start_time'] = datetime.now()

        logger.info("Starting continuous data crawling...")

        # Initial historical data crawl
        if self.config.enable_historical_data:
            logger.info("Starting initial historical data crawl...")
            await self.crawl_historical_data()

        # Continuous realtime data crawling
        while self.is_running:
            try:
                await self.crawl_realtime_data()

                # Log statistics periodically
                if self.stats['total_requests'] % 100 == 0:
                    await self._log_statistics()

                # Wait before next crawl
                await asyncio.sleep(self.config.crawl_interval)

            except Exception as e:
                logger.error(f"Error in continuous crawling: {str(e)}")
                await asyncio.sleep(5)  # Wait longer on error

    async def _log_statistics(self):
        """Log crawler statistics"""
        uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else timedelta(0)
        success_rate = (self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100

        logger.info(f"Crawler Stats - Uptime: {uptime}, "
                   f"Requests: {self.stats['total_requests']}, "
                   f"Success Rate: {success_rate:.1f}%, "
                   f"Symbols Processed: {self.stats['symbols_processed']}")

        # Log source statistics
        for source in self.data_sources:
            source_stats = source.get_stats()
            logger.info(f"{source.get_source_type().value} Stats: {source_stats}")

    def stop(self):
        """Stop the crawler"""
        self.is_running = False
        logger.info("Crawler stop requested")

    async def cleanup(self):
        """Cleanup resources"""
        for source in self.data_sources:
            await source.close_session()
        logger.info("Crawler cleanup completed")

async def main():
    """Main function to run the data crawler"""
    import signal

    # Create crawler with configuration
    config = CrawlerConfig.from_env()
    crawler = DataCrawler(config)

    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        crawler.stop()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        logger.info("Starting StockFilterX Data Crawler...")
        logger.info(f"Configuration: {config}")

        # Run the crawler
        await crawler.run_continuous_crawling()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
    finally:
        logger.info("Shutting down crawler...")
        await crawler.cleanup()
        logger.info("Crawler shutdown complete")


if __name__ == "__main__":
    asyncio.run(main())