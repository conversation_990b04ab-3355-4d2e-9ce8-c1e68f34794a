[package]
name = "stockfilterx-analysis-engine"
version = "1.0.0"
edition = "2021"
authors = ["StockFilterX Team"]
description = "High-performance stock analysis engine for StockFilterX"
license = "MIT"

[dependencies]
# Async runtime
tokio = { version = "1.35", features = ["full"] }
tokio-postgres = { version = "0.7", features = ["with-chrono-0_4", "with-serde_json-1"] }

# Redis
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Date and time
chrono = { version = "0.4", features = ["serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration
config = "0.14"
dotenvy = "0.15"

# Mathematical computations
nalgebra = "0.32"
statrs = "0.16"

# Async utilities
futures = "0.3"
async-trait = "0.1"

# Performance monitoring
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# Database connection pooling
deadpool-postgres = "0.12"

# Message queue
lapin = "2.3"  # RabbitMQ client

[dev-dependencies]
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }

[[bench]]
name = "analysis_benchmarks"
harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
