//! StockFilterX Analysis Engine
//!
//! High-performance stock analysis engine built with Rust
//! Provides real-time technical analysis and scoring for Vietnamese stocks

use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use redis::aio::ConnectionManager;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::time::{interval, Duration};
use tokio_postgres::{Client, NoTls};
use tracing::{error, info, warn, debug};

mod config;
mod database;
mod indicators;
mod models;
mod scoring;
mod message_queue;

use config::Config;
use database::DatabaseManager;
use indicators::TechnicalIndicators;
use models::{StockData, StockScore, AnalysisResult};
use scoring::ScoringEngine;
use message_queue::MessageQueue;

/// Main analysis engine that orchestrates all analysis components
pub struct AnalysisEngine {
    config: Arc<Config>,
    db_manager: Arc<DatabaseManager>,
    redis_manager: Arc<ConnectionManager>,
    scoring_engine: Arc<ScoringEngine>,
    message_queue: Arc<MessageQueue>,
    technical_indicators: Arc<TechnicalIndicators>,
}

impl AnalysisEngine {
    /// Create a new analysis engine instance
    pub async fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);

        // Initialize database manager
        let db_manager = Arc::new(
            DatabaseManager::new(&config.database_url)
                .await
                .context("Failed to initialize database manager")?
        );

        // Initialize Redis connection manager
        let redis_client = redis::Client::open(config.redis_url.as_str())
            .context("Failed to create Redis client")?;
        let redis_manager = Arc::new(
            ConnectionManager::new(redis_client)
                .await
                .context("Failed to create Redis connection manager")?
        );

        // Initialize message queue
        let message_queue = Arc::new(
            MessageQueue::new(&config.rabbitmq_url)
                .await
                .context("Failed to initialize message queue")?
        );

        // Initialize technical indicators
        let technical_indicators = Arc::new(TechnicalIndicators::new());

        // Initialize scoring engine
        let scoring_engine = Arc::new(ScoringEngine::new(
            config.clone(),
            technical_indicators.clone(),
        ));

        Ok(Self {
            config,
            db_manager,
            redis_manager,
            scoring_engine,
            message_queue,
            technical_indicators,
        })
    }

    /// Run the analysis engine
    pub async fn run(&self) -> Result<()> {
        info!("Starting StockFilterX Analysis Engine");

        // Setup metrics server
        self.setup_metrics_server().await?;

        // Start analysis loop
        let mut interval = interval(Duration::from_secs(self.config.analysis_interval));

        loop {
            interval.tick().await;

            if let Err(e) = self.run_analysis_cycle().await {
                error!("Analysis cycle failed: {}", e);
                // Continue running even if one cycle fails
            }
        }
    }

    /// Run a single analysis cycle
    async fn run_analysis_cycle(&self) -> Result<()> {
        info!("Starting analysis cycle");

        // Get all active symbols
        let symbols = self.db_manager.get_active_symbols().await
            .context("Failed to get active symbols")?;

        if symbols.is_empty() {
            warn!("No active symbols found for analysis");
            return Ok(());
        }

        info!("Analyzing {} symbols", symbols.len());

        // Process symbols in batches
        let chunks: Vec<_> = symbols.chunks(self.config.batch_size).collect();

        for chunk in chunks {
            if let Err(e) = self.process_symbol_batch(chunk).await {
                error!("Failed to process symbol batch: {}", e);
                // Continue with next batch
            }
        }

        info!("Analysis cycle completed");
        Ok(())
    }

    /// Process a batch of symbols
    async fn process_symbol_batch(&self, symbols: &[String]) -> Result<()> {
        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.config.max_concurrent_analysis));
        let mut tasks = Vec::new();

        for symbol in symbols {
            let symbol = symbol.clone();
            let semaphore = semaphore.clone();
            let engine = self.clone_for_task();

            let task = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                engine.analyze_symbol(&symbol).await
            });

            tasks.push(task);
        }

        // Wait for all tasks to complete
        let results = futures::future::join_all(tasks).await;

        let mut successful = 0;
        let mut failed = 0;

        for result in results {
            match result {
                Ok(Ok(_)) => successful += 1,
                Ok(Err(e)) => {
                    error!("Symbol analysis failed: {}", e);
                    failed += 1;
                }
                Err(e) => {
                    error!("Task failed: {}", e);
                    failed += 1;
                }
            }
        }

        info!("Batch processed: {} successful, {} failed", successful, failed);
        Ok(())
    }

    /// Analyze a single symbol
    async fn analyze_symbol(&self, symbol: &str) -> Result<()> {
        debug!("Analyzing symbol: {}", symbol);

        // Get stock data
        let stock_data = self.db_manager.get_stock_data(symbol, 100).await
            .context("Failed to get stock data")?;

        if stock_data.len() < 20 {
            warn!("Insufficient data for symbol {}: {} points", symbol, stock_data.len());
            return Ok(());
        }

        // Calculate technical indicators
        let indicators = self.technical_indicators.calculate_all_indicators(&stock_data, 20);

        // Calculate score
        let score = self.scoring_engine.calculate_score(&stock_data, &indicators);

        // Save score to database
        self.db_manager.save_stock_score(&score).await
            .context("Failed to save stock score")?;

        // Cache score in Redis
        self.cache_score_in_redis(&score).await
            .context("Failed to cache score in Redis")?;

        // Publish score to message queue
        self.message_queue.publish_score(&score).await
            .context("Failed to publish score")?;

        // Create and publish analysis result
        let analysis_result = AnalysisResult {
            symbol: symbol.to_string(),
            timestamp: chrono::Utc::now(),
            stock_data,
            technical_indicators: indicators,
            score,
            market_context: self.get_market_context().await?,
        };

        self.message_queue.publish_analysis_result(&analysis_result).await
            .context("Failed to publish analysis result")?;

        debug!("Analysis completed for symbol: {}", symbol);
        Ok(())
    }

    /// Cache score in Redis
    async fn cache_score_in_redis(&self, score: &StockScore) -> Result<()> {
        let mut conn = self.redis_manager.clone();
        let score_json = serde_json::to_string(score)
            .context("Failed to serialize score")?;

        let key = format!("stock_score:{}", score.symbol);

        redis::cmd("SETEX")
            .arg(&key)
            .arg(300) // 5 minutes TTL
            .arg(&score_json)
            .query_async(&mut conn)
            .await
            .context("Failed to cache score in Redis")?;

        Ok(())
    }

    /// Get market context (placeholder implementation)
    async fn get_market_context(&self) -> Result<models::MarketContext> {
        // This would typically analyze overall market conditions
        // For now, return a default context
        Ok(models::MarketContext {
            market_trend: models::MarketTrend::Sideways,
            sector_performance: 0.0,
            market_volatility: 0.02,
            trading_volume_trend: 1.0,
        })
    }

    /// Setup metrics server
    async fn setup_metrics_server(&self) -> Result<()> {
        // This would setup Prometheus metrics endpoint
        // For now, just log that metrics are enabled
        info!("Metrics server would be started on port {}", self.config.metrics_port);
        Ok(())
    }

    /// Clone engine for async tasks
    fn clone_for_task(&self) -> Self {
        Self {
            config: self.config.clone(),
            db_manager: self.db_manager.clone(),
            redis_manager: self.redis_manager.clone(),
            scoring_engine: self.scoring_engine.clone(),
            message_queue: self.message_queue.clone(),
            technical_indicators: self.technical_indicators.clone(),
        }
    }

    /// Health check for all components
    pub async fn health_check(&self) -> Result<()> {
        // Check database
        self.db_manager.health_check().await
            .context("Database health check failed")?;

        // Check message queue
        self.message_queue.health_check().await
            .context("Message queue health check failed")?;

        // Check Redis
        let mut conn = self.redis_manager.clone();
        redis::cmd("PING")
            .query_async::<_, String>(&mut conn)
            .await
            .context("Redis health check failed")?;

        info!("All health checks passed");
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    info!("Starting StockFilterX Analysis Engine");

    // Load configuration
    let config = Config::from_env()
        .context("Failed to load configuration")?;

    // Validate configuration
    config.validate()
        .context("Configuration validation failed")?;

    info!("Configuration loaded and validated");

    // Create and start analysis engine
    let engine = AnalysisEngine::new(config).await
        .context("Failed to create analysis engine")?;

    // Perform initial health check
    engine.health_check().await
        .context("Initial health check failed")?;

    info!("Analysis engine initialized successfully");

    // Setup signal handlers for graceful shutdown
    let (shutdown_tx, mut shutdown_rx) = tokio::sync::mpsc::channel::<()>(1);

    tokio::spawn(async move {
        let mut sigterm = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .expect("Failed to create SIGTERM handler");
        let mut sigint = tokio::signal::unix::signal(tokio::signal::unix::SignalKind::interrupt())
            .expect("Failed to create SIGINT handler");

        tokio::select! {
            _ = sigterm.recv() => {
                info!("Received SIGTERM, shutting down gracefully");
            }
            _ = sigint.recv() => {
                info!("Received SIGINT, shutting down gracefully");
            }
        }

        let _ = shutdown_tx.send(()).await;
    });

    // Run the analysis engine
    tokio::select! {
        result = engine.run() => {
            match result {
                Ok(_) => info!("Analysis engine completed successfully"),
                Err(e) => error!("Analysis engine failed: {}", e),
            }
        }
        _ = shutdown_rx.recv() => {
            info!("Shutdown signal received, stopping analysis engine");
        }
    }

    info!("StockFilterX Analysis Engine shutdown complete");
    Ok(())
}