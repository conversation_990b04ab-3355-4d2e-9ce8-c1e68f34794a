use chrono::{DateTime, Utc};
use redis::Commands;
use serde::{Deserialize, Serialize};
use std::error::Error;
use tokio_postgres::{Client, NoTls};

#[derive(Debug, Serialize, Deserialize)]
struct StockData {
    symbol: String,
    timestamp: DateTime<Utc>,
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: i64,
}

#[derive(Debug, Serialize, Deserialize)]
struct StockScore {
    symbol: String,
    money_flow_score: f64,
    accumulation_score: f64,
    momentum_score: f64,
    sector_correlation_score: f64,
    total_score: f64,
    updated_at: DateTime<Utc>,
}

struct AnalysisEngine {
    db_client: Client,
    redis_client: redis::Client,
}

impl AnalysisEngine {
    async fn new(db_url: &str, redis_url: &str) -> Result<Self, Box<dyn Error>> {
        let (db_client, connection) = tokio_postgres::connect(db_url, NoTls).await?
        tokio::spawn(async move {
            if let Err(e) = connection.await {
                eprintln!("Lỗi kết nối database: {}", e);
            }
        });

        let redis_client = redis::Client::open(redis_url)?;

        Ok(Self {
            db_client,
            redis_client,
        })
    }

    async fn calculate_money_flow_score(&self, data: &[StockData]) -> f64 {
        // Tính toán chỉ số dòng tiền (Money Flow Index)
        let mut typical_prices: Vec<f64> = Vec::new();
        let mut money_flows: Vec<f64> = Vec::new();

        for item in data {
            let typical_price = (item.high + item.low + item.close) / 3.0;
            let money_flow = typical_price * item.volume as f64;
            
            typical_prices.push(typical_price);
            money_flows.push(money_flow);
        }

        let mut positive_flow = 0.0;
        let mut negative_flow = 0.0;

        for i in 1..typical_prices.len() {
            let price_change = typical_prices[i] - typical_prices[i-1];
            if price_change > 0.0 {
                positive_flow += money_flows[i];
            } else {
                negative_flow += money_flows[i];
            }
        }

        let money_ratio = if negative_flow == 0.0 {
            100.0
        } else {
            100.0 - (100.0 / (1.0 + (positive_flow / negative_flow)))
        };

        // Chuẩn hóa điểm số về thang điểm 0-100
        money_ratio.min(100.0).max(0.0)
    }

    async fn calculate_accumulation_score(&self, data: &[StockData]) -> f64 {
        // Tính toán điểm tích lũy dựa trên khối lượng và biến động giá
        let mut accumulation_score = 0.0;
        let window_size = 20; // Chu kỳ phân tích

        if data.len() < window_size {
            return 0.0;
        }

        let recent_data = &data[data.len() - window_size..];
        let mut volume_trend = 0.0;
        let mut price_trend = 0.0;

        for i in 1..recent_data.len() {
            let vol_change = (recent_data[i].volume as f64 / recent_data[i-1].volume as f64) - 1.0;
            let price_change = (recent_data[i].close / recent_data[i-1].close) - 1.0;
            
            volume_trend += vol_change;
            price_trend += price_change;
        }

        // Tính điểm dựa trên sự kết hợp của xu hướng khối lượng và giá
        accumulation_score = (volume_trend + price_trend) * 50.0 + 50.0;
        accumulation_score.min(100.0).max(0.0)
    }

    async fn calculate_momentum_score(&self, data: &[StockData]) -> f64 {
        // Tính RSI và Momentum
        let period = 14;
        if data.len() < period {
            return 0.0;
        }

        let mut gains = 0.0;
        let mut losses = 0.0;

        for i in 1..period {
            let price_change = data[i].close - data[i-1].close;
            if price_change >= 0.0 {
                gains += price_change;
            } else {
                losses -= price_change;
            }
        }

        let avg_gain = gains / period as f64;
        let avg_loss = losses / period as f64;

        let rs = if avg_loss == 0.0 {
            100.0
        } else {
            avg_gain / avg_loss
        };

        let rsi = 100.0 - (100.0 / (1.0 + rs));

        // Tính Momentum
        let momentum = (data.last().unwrap().close / data[data.len() - period].close - 1.0) * 100.0;
        
        // Kết hợp RSI và Momentum để có điểm số cuối cùng
        let momentum_score = (rsi * 0.6 + momentum.min(100.0).max(-100.0) * 0.4 + 100.0) / 2.0;
        momentum_score.min(100.0).max(0.0)
    }

    async fn calculate_sector_correlation_score(&self, symbol: &str) -> f64 {
        // TODO: Implement sector correlation analysis
        50.0 // Giá trị mặc định
    }

    async fn analyze_stock(&self, symbol: &str) -> Result<StockScore, Box<dyn Error>> {
        // Lấy dữ liệu từ database
        let rows = self.db_client
            .query(
                "SELECT * FROM stock_data WHERE symbol = $1 ORDER BY timestamp DESC LIMIT 100",
                &[&symbol]
            )
            .await?;

        let mut data: Vec<StockData> = Vec::new();
        for row in rows {
            data.push(StockData {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                open: row.get("open"),
                high: row.get("high"),
                low: row.get("low"),
                close: row.get("close"),
                volume: row.get("volume"),
            });
        }

        // Tính toán các điểm số
        let money_flow_score = self.calculate_money_flow_score(&data).await;
        let accumulation_score = self.calculate_accumulation_score(&data).await;
        let momentum_score = self.calculate_momentum_score(&data).await;
        let sector_correlation_score = self.calculate_sector_correlation_score(symbol).await;

        // Tính tổng điểm với trọng số
        let total_score = (
            money_flow_score * 0.3 +
            accumulation_score * 0.3 +
            momentum_score * 0.25 +
            sector_correlation_score * 0.15
        );

        let score = StockScore {
            symbol: symbol.to_string(),
            money_flow_score,
            accumulation_score,
            momentum_score,
            sector_correlation_score,
            total_score,
            updated_at: Utc::now(),
        };

        // Lưu kết quả vào Redis
        let mut redis_conn = self.redis_client.get_connection()?;
        let _: () = redis_conn.set_ex(
            format!("stock_score:{}", symbol),
            serde_json::to_string(&score)?,
            300, // Cache 5 phút
        )?;

        Ok(score)
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let db_url = std::env::var("DATABASE_URL")?;
    let redis_url = std::env::var("REDIS_URL")?;

    let engine = AnalysisEngine::new(&db_url, &redis_url).await?;

    // TODO: Implement main analysis loop
    println!("Analysis Engine started");

    Ok(())
}