//! Message queue integration for the analysis engine

use anyhow::{Context, Result};
use lapin::{
    options::*, publisher_confirm::Confirmation, types::FieldTable, BasicProperties, 
    Connection, ConnectionProperties, Channel
};
use serde_json;
use tracing::{error, info, debug, warn};
use crate::models::{StockScore, AnalysisResult, AlertTrigger};

pub struct MessageQueue {
    connection: Connection,
    channel: Channel,
}

impl MessageQueue {
    /// Create a new message queue connection
    pub async fn new(rabbitmq_url: &str) -> Result<Self> {
        let connection = Connection::connect(
            rabbitmq_url,
            ConnectionProperties::default(),
        ).await
        .context("Failed to connect to RabbitMQ")?;
        
        let channel = connection.create_channel().await
            .context("Failed to create RabbitMQ channel")?;
        
        // Declare exchanges and queues
        Self::setup_exchanges_and_queues(&channel).await?;
        
        info!("Message queue connection established");
        
        Ok(Self {
            connection,
            channel,
        })
    }
    
    /// Setup exchanges and queues
    async fn setup_exchanges_and_queues(channel: &Channel) -> Result<()> {
        // Declare exchanges
        channel.exchange_declare(
            "stockfilterx.analysis",
            lapin::ExchangeKind::Topic,
            ExchangeDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await
        .context("Failed to declare analysis exchange")?;
        
        channel.exchange_declare(
            "stockfilterx.alerts",
            lapin::ExchangeKind::Topic,
            ExchangeDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await
        .context("Failed to declare alerts exchange")?;
        
        // Declare queues
        channel.queue_declare(
            "analysis.scores",
            QueueDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await
        .context("Failed to declare scores queue")?;
        
        channel.queue_declare(
            "analysis.results",
            QueueDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await
        .context("Failed to declare results queue")?;
        
        channel.queue_declare(
            "alerts.triggers",
            QueueDeclareOptions {
                durable: true,
                ..Default::default()
            },
            FieldTable::default(),
        ).await
        .context("Failed to declare alerts queue")?;
        
        // Bind queues to exchanges
        channel.queue_bind(
            "analysis.scores",
            "stockfilterx.analysis",
            "scores.*",
            QueueBindOptions::default(),
            FieldTable::default(),
        ).await
        .context("Failed to bind scores queue")?;
        
        channel.queue_bind(
            "analysis.results",
            "stockfilterx.analysis",
            "results.*",
            QueueBindOptions::default(),
            FieldTable::default(),
        ).await
        .context("Failed to bind results queue")?;
        
        channel.queue_bind(
            "alerts.triggers",
            "stockfilterx.alerts",
            "triggers.*",
            QueueBindOptions::default(),
            FieldTable::default(),
        ).await
        .context("Failed to bind alerts queue")?;
        
        info!("Message queue exchanges and queues setup completed");
        Ok(())
    }
    
    /// Publish stock score to message queue
    pub async fn publish_score(&self, score: &StockScore) -> Result<()> {
        let payload = serde_json::to_vec(score)
            .context("Failed to serialize stock score")?;
        
        let routing_key = format!("scores.{}", score.symbol.to_lowercase());
        
        let confirmation = self.channel
            .basic_publish(
                "stockfilterx.analysis",
                &routing_key,
                BasicPublishOptions::default(),
                &payload,
                BasicProperties::default()
                    .with_content_type("application/json".into())
                    .with_delivery_mode(2), // Persistent
            )
            .await
            .context("Failed to publish stock score")?
            .await
            .context("Failed to get publish confirmation")?;
        
        match confirmation {
            Confirmation::Ack(_) => {
                debug!("Successfully published score for {}", score.symbol);
                Ok(())
            }
            Confirmation::Nack(_) => {
                error!("Failed to publish score for {}", score.symbol);
                Err(anyhow::anyhow!("Message was nacked"))
            }
        }
    }
    
    /// Publish analysis result to message queue
    pub async fn publish_analysis_result(&self, result: &AnalysisResult) -> Result<()> {
        let payload = serde_json::to_vec(result)
            .context("Failed to serialize analysis result")?;
        
        let routing_key = format!("results.{}", result.symbol.to_lowercase());
        
        let confirmation = self.channel
            .basic_publish(
                "stockfilterx.analysis",
                &routing_key,
                BasicPublishOptions::default(),
                &payload,
                BasicProperties::default()
                    .with_content_type("application/json".into())
                    .with_delivery_mode(2), // Persistent
            )
            .await
            .context("Failed to publish analysis result")?
            .await
            .context("Failed to get publish confirmation")?;
        
        match confirmation {
            Confirmation::Ack(_) => {
                debug!("Successfully published analysis result for {}", result.symbol);
                Ok(())
            }
            Confirmation::Nack(_) => {
                error!("Failed to publish analysis result for {}", result.symbol);
                Err(anyhow::anyhow!("Message was nacked"))
            }
        }
    }
    
    /// Publish alert trigger to message queue
    pub async fn publish_alert(&self, alert: &AlertTrigger) -> Result<()> {
        let payload = serde_json::to_vec(alert)
            .context("Failed to serialize alert trigger")?;
        
        let routing_key = format!("triggers.{}", alert.symbol.to_lowercase());
        
        let confirmation = self.channel
            .basic_publish(
                "stockfilterx.alerts",
                &routing_key,
                BasicPublishOptions::default(),
                &payload,
                BasicProperties::default()
                    .with_content_type("application/json".into())
                    .with_delivery_mode(2), // Persistent
            )
            .await
            .context("Failed to publish alert trigger")?
            .await
            .context("Failed to get publish confirmation")?;
        
        match confirmation {
            Confirmation::Ack(_) => {
                debug!("Successfully published alert for {}", alert.symbol);
                Ok(())
            }
            Confirmation::Nack(_) => {
                error!("Failed to publish alert for {}", alert.symbol);
                Err(anyhow::anyhow!("Message was nacked"))
            }
        }
    }
    
    /// Batch publish multiple scores
    pub async fn batch_publish_scores(&self, scores: &[StockScore]) -> Result<()> {
        for score in scores {
            if let Err(e) = self.publish_score(score).await {
                warn!("Failed to publish score for {}: {}", score.symbol, e);
                // Continue with other scores even if one fails
            }
        }
        
        info!("Batch published {} scores", scores.len());
        Ok(())
    }
    
    /// Health check for message queue connection
    pub async fn health_check(&self) -> Result<()> {
        // Check if connection is still open
        if self.connection.status().connected() {
            Ok(())
        } else {
            Err(anyhow::anyhow!("Message queue connection is not active"))
        }
    }
    
    /// Close the message queue connection
    pub async fn close(&self) -> Result<()> {
        self.channel.close(200, "Normal shutdown").await
            .context("Failed to close channel")?;
        
        self.connection.close(200, "Normal shutdown").await
            .context("Failed to close connection")?;
        
        info!("Message queue connection closed");
        Ok(())
    }
}
