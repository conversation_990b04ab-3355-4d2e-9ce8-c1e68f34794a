//! Database management for the analysis engine

use crate::models::{StockData, StockScore};
use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use deadpool_postgres::{Config, Pool, Runtime};
use tokio_postgres::NoTls;
use tracing::{error, info, debug};

pub struct DatabaseManager {
    pool: Pool,
}

impl DatabaseManager {
    /// Create a new database manager
    pub async fn new(database_url: &str) -> Result<Self> {
        let mut cfg = Config::new();
        cfg.url = Some(database_url.to_string());
        cfg.manager = Some(deadpool_postgres::ManagerConfig {
            recycling_method: deadpool_postgres::RecyclingMethod::Fast,
        });
        cfg.pool = Some(deadpool_postgres::PoolConfig {
            max_size: 20,
            timeouts: deadpool_postgres::Timeouts::default(),
        });
        
        let pool = cfg.create_pool(Some(Runtime::Tokio1), NoTls)
            .context("Failed to create database pool")?;
        
        info!("Database connection pool created successfully");
        
        Ok(Self { pool })
    }
    
    /// Get stock data for analysis
    pub async fn get_stock_data(&self, symbol: &str, limit: i64) -> Result<Vec<StockData>> {
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let query = "
            SELECT symbol, timestamp, open, high, low, close, volume
            FROM stock_data 
            WHERE symbol = $1 
            ORDER BY timestamp DESC 
            LIMIT $2
        ";
        
        let rows = client.query(query, &[&symbol, &limit]).await
            .context("Failed to execute stock data query")?;
        
        let mut stock_data = Vec::new();
        for row in rows {
            stock_data.push(StockData {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                open: row.get("open"),
                high: row.get("high"),
                low: row.get("low"),
                close: row.get("close"),
                volume: row.get("volume"),
            });
        }
        
        // Reverse to get chronological order
        stock_data.reverse();
        
        debug!("Retrieved {} data points for symbol {}", stock_data.len(), symbol);
        Ok(stock_data)
    }
    
    /// Get all active symbols for analysis
    pub async fn get_active_symbols(&self) -> Result<Vec<String>> {
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let query = "
            SELECT DISTINCT symbol 
            FROM stock_data 
            WHERE timestamp > NOW() - INTERVAL '7 days'
            ORDER BY symbol
        ";
        
        let rows = client.query(query, &[]).await
            .context("Failed to execute symbols query")?;
        
        let symbols: Vec<String> = rows.iter()
            .map(|row| row.get("symbol"))
            .collect();
        
        info!("Retrieved {} active symbols", symbols.len());
        Ok(symbols)
    }
    
    /// Save stock score to database
    pub async fn save_stock_score(&self, score: &StockScore) -> Result<()> {
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let query = "
            INSERT INTO stock_scores (
                symbol, money_flow_score, accumulation_score, momentum_score,
                sector_correlation_score, volume_profile_score, volatility_score,
                total_score, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ";
        
        client.execute(query, &[
            &score.symbol,
            &score.money_flow_score,
            &score.accumulation_score,
            &score.momentum_score,
            &score.sector_correlation_score,
            &score.volume_profile_score,
            &score.volatility_score,
            &score.total_score,
            &score.updated_at,
        ]).await
        .context("Failed to save stock score")?;
        
        debug!("Saved score for symbol {}: {:.2}", score.symbol, score.total_score);
        Ok(())
    }
    
    /// Get latest stock scores
    pub async fn get_latest_scores(&self, limit: i64) -> Result<Vec<StockScore>> {
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let query = "
            SELECT DISTINCT ON (symbol) 
                symbol, money_flow_score, accumulation_score, momentum_score,
                sector_correlation_score, volume_profile_score, volatility_score,
                total_score, updated_at
            FROM stock_scores 
            ORDER BY symbol, updated_at DESC
            LIMIT $1
        ";
        
        let rows = client.query(query, &[&limit]).await
            .context("Failed to execute latest scores query")?;
        
        let mut scores = Vec::new();
        for row in rows {
            scores.push(StockScore {
                symbol: row.get("symbol"),
                money_flow_score: row.get("money_flow_score"),
                accumulation_score: row.get("accumulation_score"),
                momentum_score: row.get("momentum_score"),
                sector_correlation_score: row.get("sector_correlation_score"),
                volume_profile_score: row.get("volume_profile_score"),
                volatility_score: row.get("volatility_score"),
                total_score: row.get("total_score"),
                updated_at: row.get("updated_at"),
                confidence: 0.8, // Default confidence
                recommendation: crate::models::Recommendation::from_score(row.get("total_score")),
            });
        }
        
        debug!("Retrieved {} latest scores", scores.len());
        Ok(scores)
    }
    
    /// Get stock data in date range
    pub async fn get_stock_data_range(
        &self, 
        symbol: &str, 
        start_date: DateTime<Utc>, 
        end_date: DateTime<Utc>
    ) -> Result<Vec<StockData>> {
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let query = "
            SELECT symbol, timestamp, open, high, low, close, volume
            FROM stock_data 
            WHERE symbol = $1 AND timestamp BETWEEN $2 AND $3
            ORDER BY timestamp ASC
        ";
        
        let rows = client.query(query, &[&symbol, &start_date, &end_date]).await
            .context("Failed to execute stock data range query")?;
        
        let mut stock_data = Vec::new();
        for row in rows {
            stock_data.push(StockData {
                symbol: row.get("symbol"),
                timestamp: row.get("timestamp"),
                open: row.get("open"),
                high: row.get("high"),
                low: row.get("low"),
                close: row.get("close"),
                volume: row.get("volume"),
            });
        }
        
        debug!("Retrieved {} data points for symbol {} in date range", stock_data.len(), symbol);
        Ok(stock_data)
    }
    
    /// Batch save stock scores
    pub async fn batch_save_scores(&self, scores: &[StockScore]) -> Result<()> {
        if scores.is_empty() {
            return Ok(());
        }
        
        let client = self.pool.get().await
            .context("Failed to get database connection")?;
        
        let mut query = String::from("
            INSERT INTO stock_scores (
                symbol, money_flow_score, accumulation_score, momentum_score,
                sector_correlation_score, volume_profile_score, volatility_score,
                total_score, updated_at
            ) VALUES 
        ");
        
        let mut params: Vec<&(dyn tokio_postgres::types::ToSql + Sync)> = Vec::new();
        let mut param_index = 1;
        
        for (i, score) in scores.iter().enumerate() {
            if i > 0 {
                query.push_str(", ");
            }
            
            query.push_str(&format!(
                "(${}, ${}, ${}, ${}, ${}, ${}, ${}, ${}, ${})",
                param_index, param_index + 1, param_index + 2, param_index + 3,
                param_index + 4, param_index + 5, param_index + 6, param_index + 7,
                param_index + 8
            ));
            
            params.extend_from_slice(&[
                &score.symbol,
                &score.money_flow_score,
                &score.accumulation_score,
                &score.momentum_score,
                &score.sector_correlation_score,
                &score.volume_profile_score,
                &score.volatility_score,
                &score.total_score,
                &score.updated_at,
            ]);
            
            param_index += 9;
        }
        
        client.execute(&query, &params).await
            .context("Failed to batch save stock scores")?;
        
        info!("Batch saved {} stock scores", scores.len());
        Ok(())
    }
    
    /// Health check for database connection
    pub async fn health_check(&self) -> Result<()> {
        let client = self.pool.get().await
            .context("Failed to get database connection for health check")?;
        
        client.query("SELECT 1", &[]).await
            .context("Database health check failed")?;
        
        Ok(())
    }
}
