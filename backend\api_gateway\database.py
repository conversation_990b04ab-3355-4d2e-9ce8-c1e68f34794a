"""
Database configuration and connection management for StockFilterX
"""
import logging
from typing import AsyncGenerator
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import NullPool
from databases import Database
from config import settings

logger = logging.getLogger(__name__)

# Database URL with proper configuration
DATABASE_URL = settings.DATABASE_URL

# Create async engine with connection pooling
engine = create_async_engine(
    DATABASE_URL,
    echo=settings.DEBUG,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_pre_ping=True,
    pool_recycle=3600,  # Recycle connections every hour
)

# Session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False
)

# Database instance for direct queries
database = Database(DATABASE_URL)

# Base class for models
Base = declarative_base()

# Metadata for TimescaleDB
metadata = MetaData()


async def get_database() -> Database:
    """Get database instance"""
    return database


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {str(e)}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db():
    """Initialize database and create tables"""
    try:
        logger.info("Initializing database...")

        # Connect to database
        await database.connect()

        # Create TimescaleDB extension if not exists
        await database.execute("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;")

        # Create tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # Create hypertables for time-series data
        await create_hypertables()

        logger.info("Database initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise


async def create_hypertables():
    """Create TimescaleDB hypertables for time-series data"""
    try:
        # Create hypertable for stock data
        await database.execute("""
            SELECT create_hypertable('stock_data', 'timestamp',
                                   if_not_exists => TRUE);
        """)

        # Create hypertable for stock scores
        await database.execute("""
            SELECT create_hypertable('stock_scores', 'updated_at',
                                   if_not_exists => TRUE);
        """)

        logger.info("Hypertables created successfully")

    except Exception as e:
        logger.warning(f"Failed to create hypertables: {str(e)}")


async def close_db():
    """Close database connections"""
    try:
        await database.disconnect()
        await engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")


# Health check function
async def check_db_health() -> bool:
    """Check database health"""
    try:
        await database.fetch_one("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return False