<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .score-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .score-title {
            font-size: 18px;
            font-weight: bold;
            color: #155724;
            margin-bottom: 10px;
        }
        .score-value {
            font-size: 36px;
            font-weight: bold;
            color: #28a745;
            margin: 10px 0;
        }
        .score-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 20px 0;
        }
        .score-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .score-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .score-number {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">StockFilterX</div>
            <p>Cập nhật điểm số cổ phiếu</p>
        </div>
        
        <div class="score-box">
            <div class="score-title">{{ title }}</div>
            <div class="score-value">{{ data.total_score if data and data.total_score else 'N/A' }}</div>
            <p>{{ message }}</p>
        </div>
        
        {% if data %}
        <div class="score-details">
            {% if data.money_flow_score %}
            <div class="score-item">
                <div class="score-label">Dòng tiền</div>
                <div class="score-number">{{ "%.1f"|format(data.money_flow_score) }}</div>
            </div>
            {% endif %}
            {% if data.accumulation_score %}
            <div class="score-item">
                <div class="score-label">Tích lũy</div>
                <div class="score-number">{{ "%.1f"|format(data.accumulation_score) }}</div>
            </div>
            {% endif %}
            {% if data.momentum_score %}
            <div class="score-item">
                <div class="score-label">Động lượng</div>
                <div class="score-number">{{ "%.1f"|format(data.momentum_score) }}</div>
            </div>
            {% endif %}
            {% if data.sector_correlation_score %}
            <div class="score-item">
                <div class="score-label">Tương quan ngành</div>
                <div class="score-number">{{ "%.1f"|format(data.sector_correlation_score) }}</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        <div style="text-align: center;">
            <a href="https://stockfilterx.com/dashboard" class="button">Xem chi tiết</a>
        </div>
        
        <div class="footer">
            <p>Thời gian: {{ timestamp }}</p>
            <p>Điểm số được cập nhật tự động dựa trên phân tích kỹ thuật.</p>
            <p>Để hủy đăng ký, vui lòng truy cập cài đặt tài khoản.</p>
        </div>
    </div>
</body>
</html>
