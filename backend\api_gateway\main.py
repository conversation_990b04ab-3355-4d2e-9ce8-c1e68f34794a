from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
import redis
import json
import os
import logging
from contextlib import asynccontextmanager

# Import routers
from routers import auth, watchlist, alerts
from database import get_database
from auth import get_current_user
from models import User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Lifespan context manager for startup/shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting StockFilterX API Gateway...")
    yield
    # Shutdown
    logger.info("Shutting down StockFilterX API Gateway...")

app = FastAPI(
    title="StockFilterX API",
    description="API Gateway cho hệ thống StockFilterX - Bộ lọc cổ phiếu thông minh",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure properly for production
)

# CORS middleware
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Redis connection with connection pooling
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
redis_client = redis.from_url(
    redis_url,
    decode_responses=True,
    max_connections=20,
    retry_on_timeout=True
)

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(watchlist.router, prefix="/api/v1/watchlist", tags=["Watchlist"])
app.include_router(alerts.router, prefix="/api/v1/alerts", tags=["Alerts"])

class StockScore(BaseModel):
    symbol: str
    money_flow_score: float
    accumulation_score: float
    momentum_score: float
    sector_correlation_score: float
    total_score: float
    updated_at: datetime

class HealthCheck(BaseModel):
    status: str
    timestamp: datetime
    version: str
    services: dict

# Health check endpoint
@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check Redis connection
        redis_status = "healthy"
        try:
            redis_client.ping()
        except Exception:
            redis_status = "unhealthy"

        # Check database connection
        db_status = "healthy"
        try:
            db = await get_database()
            await db.fetch("SELECT 1")
        except Exception:
            db_status = "unhealthy"

        overall_status = "healthy" if redis_status == "healthy" and db_status == "healthy" else "unhealthy"

        return HealthCheck(
            status=overall_status,
            timestamp=datetime.utcnow(),
            version="1.0.0",
            services={
                "redis": redis_status,
                "database": db_status
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "StockFilterX API Gateway",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/api/v1/stocks/scores", response_model=List[StockScore])
async def get_stock_scores(
    symbols: Optional[str] = None,
    min_score: Optional[float] = None,
    limit: int = 50
):
    """Lấy điểm số của các mã cổ phiếu theo các tiêu chí"""
    try:
        # Lấy dữ liệu từ Redis cache
        scores = []
        if symbols:
            symbol_list = symbols.split(",")
            for symbol in symbol_list:
                score_data = redis_client.get(f"stock_score:{symbol}")
                if score_data:
                    scores.append(json.loads(score_data))
        else:
            # Lấy tất cả điểm số và sắp xếp theo tổng điểm
            all_scores = []
            for key in redis_client.scan_iter("stock_score:*"):
                score_data = redis_client.get(key)
                if score_data:
                    score = json.loads(score_data)
                    if not min_score or score["total_score"] >= min_score:
                        all_scores.append(score)
            
            # Sắp xếp theo tổng điểm và giới hạn số lượng kết quả
            scores = sorted(
                all_scores,
                key=lambda x: x["total_score"],
                reverse=True
            )[:limit]

        return scores
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/stocks/{symbol}/history")
async def get_stock_history(
    symbol: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None
):
    """Lấy lịch sử điểm số của một mã cổ phiếu"""
    try:
        # Implement logic to get historical scores from TimescaleDB
        pass
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/stocks/alerts")
async def get_stock_alerts(
    min_score: Optional[float] = None,
    alert_type: Optional[str] = None
):
    """Lấy các cảnh báo về cổ phiếu"""
    try:
        # Implement logic to get alerts from notification service
        pass
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)