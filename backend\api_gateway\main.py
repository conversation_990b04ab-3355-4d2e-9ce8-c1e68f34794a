from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
import redis
import json
import os

app = FastAPI(
    title="StockFilterX API",
    description="API Gateway cho hệ thống StockFilterX",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Redis connection
redis_client = redis.Redis(
    host=os.getenv("REDIS_HOST", "localhost"),
    port=int(os.getenv("REDIS_PORT", 6379)),
    db=0,
    decode_responses=True
)

class StockScore(BaseModel):
    symbol: str
    money_flow_score: float
    accumulation_score: float
    momentum_score: float
    sector_correlation_score: float
    total_score: float
    updated_at: datetime

@app.get("/api/v1/stocks/scores", response_model=List[StockScore])
async def get_stock_scores(
    symbols: Optional[str] = None,
    min_score: Optional[float] = None,
    limit: int = 50
):
    """<PERSON><PERSON><PERSON> đi<PERSON>m số của các mã cổ phiếu theo các tiêu chí"""
    try:
        # Lấy dữ liệu từ Redis cache
        scores = []
        if symbols:
            symbol_list = symbols.split(",")
            for symbol in symbol_list:
                score_data = redis_client.get(f"stock_score:{symbol}")
                if score_data:
                    scores.append(json.loads(score_data))
        else:
            # Lấy tất cả điểm số và sắp xếp theo tổng điểm
            all_scores = []
            for key in redis_client.scan_iter("stock_score:*"):
                score_data = redis_client.get(key)
                if score_data:
                    score = json.loads(score_data)
                    if not min_score or score["total_score"] >= min_score:
                        all_scores.append(score)
            
            # Sắp xếp theo tổng điểm và giới hạn số lượng kết quả
            scores = sorted(
                all_scores,
                key=lambda x: x["total_score"],
                reverse=True
            )[:limit]

        return scores
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/stocks/{symbol}/history")
async def get_stock_history(
    symbol: str,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None
):
    """Lấy lịch sử điểm số của một mã cổ phiếu"""
    try:
        # Implement logic to get historical scores from TimescaleDB
        pass
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/stocks/alerts")
async def get_stock_alerts(
    min_score: Optional[float] = None,
    alert_type: Optional[str] = None
):
    """Lấy các cảnh báo về cổ phiếu"""
    try:
        # Implement logic to get alerts from notification service
        pass
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)