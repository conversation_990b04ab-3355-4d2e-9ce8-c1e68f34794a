//! Technical indicators implementation

use crate::models::{StockData, TechnicalIndicators, MacdValues, BollingerBands, StochasticValues, VolumeIndicators};
use std::collections::HashMap;

pub struct TechnicalIndicators;

impl TechnicalIndicators {
    pub fn new() -> Self {
        Self
    }
    
    /// Calculate RSI (Relative Strength Index)
    pub fn calculate_rsi(&self, prices: &[f64], period: usize) -> Option<f64> {
        if prices.len() < period + 1 {
            return None;
        }
        
        let mut gains = 0.0;
        let mut losses = 0.0;
        
        // Calculate initial average gain and loss
        for i in 1..=period {
            let change = prices[i] - prices[i - 1];
            if change > 0.0 {
                gains += change;
            } else {
                losses -= change;
            }
        }
        
        let mut avg_gain = gains / period as f64;
        let mut avg_loss = losses / period as f64;
        
        // Calculate RSI using <PERSON>'s smoothing
        for i in (period + 1)..prices.len() {
            let change = prices[i] - prices[i - 1];
            let gain = if change > 0.0 { change } else { 0.0 };
            let loss = if change < 0.0 { -change } else { 0.0 };
            
            avg_gain = (avg_gain * (period - 1) as f64 + gain) / period as f64;
            avg_loss = (avg_loss * (period - 1) as f64 + loss) / period as f64;
        }
        
        if avg_loss == 0.0 {
            return Some(100.0);
        }
        
        let rs = avg_gain / avg_loss;
        Some(100.0 - (100.0 / (1.0 + rs)))
    }
    
    /// Calculate MACD (Moving Average Convergence Divergence)
    pub fn calculate_macd(&self, prices: &[f64], fast: usize, slow: usize, signal: usize) -> Option<MacdValues> {
        if prices.len() < slow {
            return None;
        }
        
        let ema_fast = self.calculate_ema(prices, fast)?;
        let ema_slow = self.calculate_ema(prices, slow)?;
        
        let macd_line = ema_fast - ema_slow;
        
        // Calculate signal line (EMA of MACD line)
        let macd_values: Vec<f64> = prices.windows(slow)
            .map(|window| {
                let fast_ema = self.calculate_ema(window, fast).unwrap_or(0.0);
                let slow_ema = self.calculate_ema(window, slow).unwrap_or(0.0);
                fast_ema - slow_ema
            })
            .collect();
        
        let signal_line = self.calculate_ema(&macd_values, signal)?;
        let histogram = macd_line - signal_line;
        
        Some(MacdValues {
            macd_line,
            signal_line,
            histogram,
        })
    }
    
    /// Calculate Bollinger Bands
    pub fn calculate_bollinger_bands(&self, prices: &[f64], period: usize, std_dev: f64) -> Option<BollingerBands> {
        if prices.len() < period {
            return None;
        }
        
        let middle_band = self.calculate_sma(prices, period)?;
        let variance = self.calculate_variance(prices, period, middle_band)?;
        let standard_deviation = variance.sqrt();
        
        let upper_band = middle_band + (std_dev * standard_deviation);
        let lower_band = middle_band - (std_dev * standard_deviation);
        
        let bandwidth = (upper_band - lower_band) / middle_band;
        let current_price = *prices.last()?;
        let percent_b = (current_price - lower_band) / (upper_band - lower_band);
        
        Some(BollingerBands {
            upper_band,
            middle_band,
            lower_band,
            bandwidth,
            percent_b,
        })
    }
    
    /// Calculate Stochastic Oscillator
    pub fn calculate_stochastic(&self, highs: &[f64], lows: &[f64], closes: &[f64], k_period: usize, d_period: usize) -> Option<StochasticValues> {
        if highs.len() < k_period || lows.len() < k_period || closes.len() < k_period {
            return None;
        }
        
        let current_close = *closes.last()?;
        let period_high = highs.iter().rev().take(k_period).fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let period_low = lows.iter().rev().take(k_period).fold(f64::INFINITY, |a, &b| a.min(b));
        
        let k_percent = if period_high == period_low {
            50.0
        } else {
            ((current_close - period_low) / (period_high - period_low)) * 100.0
        };
        
        // Calculate %D as SMA of %K
        let k_values: Vec<f64> = (0..d_period.min(closes.len()))
            .map(|i| {
                let end_idx = closes.len() - i;
                let start_idx = end_idx.saturating_sub(k_period);
                
                if start_idx >= end_idx {
                    return 50.0;
                }
                
                let period_high = highs[start_idx..end_idx].iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
                let period_low = lows[start_idx..end_idx].iter().fold(f64::INFINITY, |a, &b| a.min(b));
                let close = closes[end_idx - 1];
                
                if period_high == period_low {
                    50.0
                } else {
                    ((close - period_low) / (period_high - period_low)) * 100.0
                }
            })
            .collect();
        
        let d_percent = k_values.iter().sum::<f64>() / k_values.len() as f64;
        
        Some(StochasticValues {
            k_percent,
            d_percent,
        })
    }
    
    /// Calculate Money Flow Index
    pub fn calculate_money_flow_index(&self, data: &[StockData], period: usize) -> Option<f64> {
        if data.len() < period + 1 {
            return None;
        }
        
        let mut positive_flow = 0.0;
        let mut negative_flow = 0.0;
        
        for i in 1..=period {
            let current = &data[data.len() - i];
            let previous = &data[data.len() - i - 1];
            
            let typical_price_current = current.typical_price();
            let typical_price_previous = previous.typical_price();
            let money_flow = typical_price_current * current.volume as f64;
            
            if typical_price_current > typical_price_previous {
                positive_flow += money_flow;
            } else if typical_price_current < typical_price_previous {
                negative_flow += money_flow;
            }
        }
        
        if negative_flow == 0.0 {
            return Some(100.0);
        }
        
        let money_ratio = positive_flow / negative_flow;
        Some(100.0 - (100.0 / (1.0 + money_ratio)))
    }
    
    /// Calculate Volume Weighted Average Price (VWAP)
    pub fn calculate_vwap(&self, data: &[StockData]) -> f64 {
        let mut total_volume = 0i64;
        let mut total_price_volume = 0.0;
        
        for stock_data in data {
            let typical_price = stock_data.typical_price();
            total_price_volume += typical_price * stock_data.volume as f64;
            total_volume += stock_data.volume;
        }
        
        if total_volume == 0 {
            0.0
        } else {
            total_price_volume / total_volume as f64
        }
    }
    
    /// Calculate On-Balance Volume
    pub fn calculate_obv(&self, data: &[StockData]) -> f64 {
        if data.len() < 2 {
            return 0.0;
        }
        
        let mut obv = 0.0;
        
        for i in 1..data.len() {
            let current = &data[i];
            let previous = &data[i - 1];
            
            if current.close > previous.close {
                obv += current.volume as f64;
            } else if current.close < previous.close {
                obv -= current.volume as f64;
            }
            // If close prices are equal, OBV remains unchanged
        }
        
        obv
    }
    
    /// Calculate Simple Moving Average
    pub fn calculate_sma(&self, prices: &[f64], period: usize) -> Option<f64> {
        if prices.len() < period {
            return None;
        }
        
        let sum: f64 = prices.iter().rev().take(period).sum();
        Some(sum / period as f64)
    }
    
    /// Calculate Exponential Moving Average
    pub fn calculate_ema(&self, prices: &[f64], period: usize) -> Option<f64> {
        if prices.len() < period {
            return None;
        }
        
        let multiplier = 2.0 / (period as f64 + 1.0);
        let mut ema = prices.iter().take(period).sum::<f64>() / period as f64;
        
        for &price in prices.iter().skip(period) {
            ema = (price * multiplier) + (ema * (1.0 - multiplier));
        }
        
        Some(ema)
    }
    
    /// Calculate variance for a given period
    fn calculate_variance(&self, prices: &[f64], period: usize, mean: f64) -> Option<f64> {
        if prices.len() < period {
            return None;
        }
        
        let variance: f64 = prices
            .iter()
            .rev()
            .take(period)
            .map(|&price| (price - mean).powi(2))
            .sum::<f64>() / period as f64;
        
        Some(variance)
    }
    
    /// Calculate all technical indicators for given stock data
    pub fn calculate_all_indicators(&self, data: &[StockData], period: usize) -> TechnicalIndicators {
        let closes: Vec<f64> = data.iter().map(|d| d.close).collect();
        let highs: Vec<f64> = data.iter().map(|d| d.high).collect();
        let lows: Vec<f64> = data.iter().map(|d| d.low).collect();
        let volumes: Vec<f64> = data.iter().map(|d| d.volume as f64).collect();
        
        let rsi = self.calculate_rsi(&closes, 14);
        let macd = self.calculate_macd(&closes, 12, 26, 9);
        let bollinger_bands = self.calculate_bollinger_bands(&closes, 20, 2.0);
        let stochastic = self.calculate_stochastic(&highs, &lows, &closes, 14, 3);
        
        let mut moving_averages = HashMap::new();
        if let Some(sma_20) = self.calculate_sma(&closes, 20) {
            moving_averages.insert("SMA_20".to_string(), sma_20);
        }
        if let Some(sma_50) = self.calculate_sma(&closes, 50) {
            moving_averages.insert("SMA_50".to_string(), sma_50);
        }
        if let Some(ema_12) = self.calculate_ema(&closes, 12) {
            moving_averages.insert("EMA_12".to_string(), ema_12);
        }
        if let Some(ema_26) = self.calculate_ema(&closes, 26) {
            moving_averages.insert("EMA_26".to_string(), ema_26);
        }
        
        let volume_sma = self.calculate_sma(&volumes, 20).unwrap_or(0.0);
        let current_volume = data.last().map(|d| d.volume as f64).unwrap_or(0.0);
        let volume_ratio = if volume_sma > 0.0 { current_volume / volume_sma } else { 0.0 };
        let money_flow_index = self.calculate_money_flow_index(data, 14);
        let on_balance_volume = self.calculate_obv(data);
        let volume_weighted_average_price = self.calculate_vwap(data);
        
        let volume_indicators = VolumeIndicators {
            volume_sma,
            volume_ratio,
            money_flow_index,
            on_balance_volume,
            volume_weighted_average_price,
        };
        
        TechnicalIndicators {
            rsi,
            macd,
            bollinger_bands,
            moving_averages,
            stochastic,
            volume_indicators,
        }
    }
}
