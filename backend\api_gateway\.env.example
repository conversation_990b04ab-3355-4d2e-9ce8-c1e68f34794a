# Database Configuration
DATABASE_URL=postgresql://stockfilterx:stockfilterx@localhost:5432/stockfilterx
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stockfilterx
DB_USER=stockfilterx
DB_PASSWORD=stockfilterx

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=StockFilterX
VERSION=1.0.0

# External APIs
FIREANT_API_URL=https://restv2.fireant.vn
CAFEF_API_URL=https://cafef.vn
API_RATE_LIMIT=100

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Environment
ENVIRONMENT=development

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Security
TRUSTED_HOSTS=localhost,127.0.0.1
ENABLE_HTTPS_REDIRECT=false
