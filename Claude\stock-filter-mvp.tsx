import React, { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { CirclePlus, Filter, TrendingUp, Activity, Layers, ArrowUpRight, EyeIcon, BarChart3, HelpCircle } from 'lucide-react';

// Dữ liệu mẫu
const sampleStocks = [
  { id: 1, code: 'VNM', name: '<PERSON>ami<PERSON>', price: 86500, priceChange: 2.5, volume: 1250000, avgVolume: 750000, 
    flowScore: 22, accumulationScore: 18, momentumScore: 21, sectorScore: 20, totalScore: 81, 
    sector: 'F&B', rsi: 65, macd: 1.2, trendDays: 5, notes: 'Đang bứt phá khỏi vùng tích lũy 3 tuần' },
  { id: 2, code: 'HPG', name: 'Hòa Phát Group', price: 24900, priceChange: 3.8, volume: 4500000, avgVolume: 2200000, 
    flowScore: 24, accumulationScore: 15, momentumScore: 23, sectorScore: 19, totalScore: 81, 
    sector: 'Thép', rsi: 68, macd: 1.5, trendDays: 3, notes: 'Volume tăng đột biến, MACD cắt signal' },
  { id: 3, code: 'MSN', name: 'Masan Group', price: 92600, priceChange: 1.2, volume: 850000, avgVolume: 620000, 
    flowScore: 18, accumulationScore: 21, momentumScore: 18, sectorScore: 17, totalScore: 74, 
    sector: 'Tiêu dùng', rsi: 58, macd: 0.9, trendDays: 2, notes: 'Đang tích lũy đẹp, khối ngoại mua ròng' },
  { id: 4, code: 'MWG', name: 'Thế Giới Di Động', price: 58200, priceChange: 4.2, volume: 1850000, avgVolume: 900000, 
    flowScore: 23, accumulationScore: 17, momentumScore: 22, sectorScore: 20, totalScore: 82, 
    sector: 'Bán lẻ', rsi: 70, macd: 1.8, trendDays: 7, notes: 'Breakout kèm volume, xu hướng mạnh' },
  { id: 5, code: 'VHM', name: 'Vinhomes', price: 45800, priceChange: -0.5, volume: 980000, avgVolume: 1200000, 
    flowScore: 12, accumulationScore: 20, momentumScore: 14, sectorScore: 15, totalScore: 61, 
    sector: 'BĐS', rsi: 48, macd: -0.3, trendDays: 0, notes: 'Đang tích lũy, chờ tín hiệu dòng tiền' },
  { id: 6, code: 'FPT', name: 'FPT Corp', price: 112500, priceChange: 3.1, volume: 1420000, avgVolume: 950000, 
    flowScore: 21, accumulationScore: 16, momentumScore: 23, sectorScore: 24, totalScore: 84, 
    sector: 'CNTT', rsi: 72, macd: 2.1, trendDays: 8, notes: 'Nhóm dẫn dắt, momentum rất mạnh' },
  { id: 7, code: 'VCB', name: 'Vietcombank', price: 89200, priceChange: 1.8, volume: 1150000, avgVolume: 880000, 
    flowScore: 19, accumulationScore: 17, momentumScore: 20, sectorScore: 22, totalScore: 78, 
    sector: 'Ngân hàng', rsi: 62, macd: 1.1, trendDays: 4, notes: 'Đang dẫn dắt nhóm ngân hàng' },
  { id: 8, code: 'TCB', name: 'Techcombank', price: 34500, priceChange: 2.7, volume: 2100000, avgVolume: 1450000, 
    flowScore: 20, accumulationScore: 14, momentumScore: 21, sectorScore: 18, totalScore: 73, 
    sector: 'Ngân hàng', rsi: 63, macd: 0.8, trendDays: 3, notes: 'Volume tăng, vượt MA20' },
  { id: 9, code: 'GAS', name: 'PV Gas', price: 98400, priceChange: -1.2, volume: 420000, avgVolume: 580000, 
    flowScore: 10, accumulationScore: 22, momentumScore: 12, sectorScore: 13, totalScore: 57, 
    sector: 'Dầu khí', rsi: 42, macd: -0.5, trendDays: 0, notes: 'Đang tích lũy sideway, chờ breakout' },
  { id: 10, code: 'POW', name: 'PV Power', price: 12800, priceChange: 6.5, volume: 5800000, avgVolume: 2200000, 
    flowScore: 25, accumulationScore: 19, momentumScore: 24, sectorScore: 18, totalScore: 86, 
    sector: 'Điện lực', rsi: 75, macd: 2.4, trendDays: 5, notes: 'Bứt phá mạnh kèm volume đột biến' }
];

// Dữ liệu biểu đồ mẫu
const chartData = [
  { date: '01/05', price: 80000, volume: 950000 },
  { date: '02/05', price: 79800, volume: 820000 },
  { date: '03/05', price: 80500, volume: 780000 },
  { date: '04/05', price: 81200, volume: 920000 },
  { date: '05/05', price: 82000, volume: 1050000 },
  { date: '08/05', price: 83500, volume: 1150000 },
  { date: '09/05', price: 84200, volume: 1250000 },
  { date: '10/05', price: 85700, volume: 1400000 },
  { date: '11/05', price: 84800, volume: 1100000 },
  { date: '12/05', price: 86500, volume: 1250000 },
];

// Component chính
const StockFilterApp = () => {
  const [stocks, setStocks] = useState(sampleStocks);
  const [filteredStocks, setFilteredStocks] = useState(sampleStocks);
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedStock, setSelectedStock] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const applyFilter = (filterType) => {
    setActiveFilter(filterType);
    let result = [...stocks];
    
    switch(filterType) {
      case 'flow':
        result = stocks.filter(stock => stock.flowScore >= 20);
        break;
      case 'accumulation':
        result = stocks.filter(stock => stock.accumulationScore >= 20);
        break;
      case 'momentum':
        result = stocks.filter(stock => stock.momentumScore >= 20);
        break;
      case 'sector':
        result = stocks.filter(stock => stock.sectorScore >= 20);
        break;
      case 'strong':
        result = stocks.filter(stock => stock.totalScore >= 80);
        break;
      default:
        result = stocks;
    }
    
    // Áp dụng tìm kiếm nếu có
    if (searchTerm) {
      result = result.filter(stock => 
        stock.code.toLowerCase().includes(searchTerm.toLowerCase()) || 
        stock.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    setFilteredStocks(result);
  };

  // Xử lý tìm kiếm
  const handleSearch = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
    
    let result = [...stocks];
    if (activeFilter !== 'all') {
      applyFilter(activeFilter);
    } else if (term) {
      result = stocks.filter(stock => 
        stock.code.toLowerCase().includes(term.toLowerCase()) || 
        stock.name.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredStocks(result);
    } else {
      setFilteredStocks(stocks);
    }
  };

  // Xử lý khi chọn một cổ phiếu
  const handleSelectStock = (stock) => {
    setSelectedStock(stock);
  };

  // Rating component
  const RatingBar = ({ score, maxScore = 25 }) => {
    const percent = (score / maxScore) * 100;
    const getColor = () => {
      if (percent >= 80) return 'bg-green-500';
      if (percent >= 60) return 'bg-blue-500';
      if (percent >= 40) return 'bg-yellow-500';
      return 'bg-red-500';
    };
    
    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${getColor()}`} 
          style={{ width: `${percent}%` }}
        ></div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-blue-600 text-white p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">Bộ Lọc Cổ Phiếu</h1>
          <div className="flex space-x-2">
            <button className="p-2 rounded-full bg-blue-500 hover:bg-blue-400">
              <HelpCircle size={18} />
            </button>
          </div>
        </div>
        
        {/* Search bar */}
        <div className="mt-4 relative">
          <input
            type="text"
            placeholder="Tìm mã cổ phiếu..."
            className="w-full p-2 pl-10 rounded-lg text-black"
            value={searchTerm}
            onChange={handleSearch}
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </header>
      
      {/* Filter Tabs */}
      <div className="flex p-2 bg-white overflow-x-auto sticky top-0 z-10 shadow-sm">
        <button 
          className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm ${activeFilter === 'all' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}
          onClick={() => applyFilter('all')}
        >
          <Filter size={16} className="mr-1" /> Tất cả
        </button>
        <button 
          className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm ${activeFilter === 'flow' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}
          onClick={() => applyFilter('flow')}
        >
          <TrendingUp size={16} className="mr-1" /> Dòng tiền mạnh
        </button>
        <button 
          className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm ${activeFilter === 'accumulation' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}
          onClick={() => applyFilter('accumulation')}
        >
          <Layers size={16} className="mr-1" /> Tích lũy đẹp
        </button>
        <button 
          className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm ${activeFilter === 'momentum' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}
          onClick={() => applyFilter('momentum')}
        >
          <Activity size={16} className="mr-1" /> Momentum cao
        </button>
        <button 
          className={`flex items-center px-3 py-2 mx-1 rounded-lg text-sm ${activeFilter === 'strong' ? 'bg-red-100 text-red-600' : 'bg-gray-100'}`}
          onClick={() => applyFilter('strong')}
        >
          <ArrowUpRight size={16} className="mr-1" /> Siêu mạnh
        </button>
      </div>
      
      {/* Main content */}
      <div className="flex flex-col md:flex-row flex-grow overflow-hidden">
        {/* Stock list */}
        <div className="w-full md:w-1/2 lg:w-2/5 overflow-y-auto p-2 border-r">
          <div className="text-sm font-medium text-gray-500 mb-2 px-2">
            {filteredStocks.length} cổ phiếu phù hợp tiêu chí
          </div>
          
          <div className="space-y-2">
            {filteredStocks.map(stock => (
              <div 
                key={stock.id}
                className={`bg-white rounded-lg shadow-sm p-3 cursor-pointer transition-all hover:shadow ${selectedStock && selectedStock.id === stock.id ? 'border-l-4 border-blue-500' : ''}`}
                onClick={() => handleSelectStock(stock)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <span className="font-bold text-lg">{stock.code}</span>
                      <span className="ml-2 text-xs px-2 py-0.5 rounded bg-gray-100">{stock.sector}</span>
                    </div>
                    <div className="text-sm text-gray-600">{stock.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{stock.price.toLocaleString()}</div>
                    <div className={`text-sm ${stock.priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {stock.priceChange >= 0 ? '+' : ''}{stock.priceChange}%
                    </div>
                  </div>
                </div>
                
                <div className="mt-3 grid grid-cols-4 gap-2">
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Dòng tiền</div>
                    <div className="font-medium text-sm">{stock.flowScore}</div>
                    <RatingBar score={stock.flowScore} />
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Tích lũy</div>
                    <div className="font-medium text-sm">{stock.accumulationScore}</div>
                    <RatingBar score={stock.accumulationScore} />
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Momentum</div>
                    <div className="font-medium text-sm">{stock.momentumScore}</div>
                    <RatingBar score={stock.momentumScore} />
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Ngành</div>
                    <div className="font-medium text-sm">{stock.sectorScore}</div>
                    <RatingBar score={stock.sectorScore} />
                  </div>
                </div>
                
                <div className="mt-3 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="font-medium">Tổng điểm: </span>
                    <span className={`font-bold ${
                      stock.totalScore >= 85 ? 'text-red-600' : 
                      stock.totalScore >= 70 ? 'text-green-600' : 
                      stock.totalScore >= 50 ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {stock.totalScore}/100
                    </span>
                  </div>
                  <div className="flex items-center text-xs text-blue-600">
                    <EyeIcon size={14} className="mr-1" /> Chi tiết
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Stock detail */}
        <div className="w-full md:w-1/2 lg:w-3/5 overflow-y-auto p-4 bg-white">
          {selectedStock ? (
            <div>
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-2xl font-bold">{selectedStock.code}</h2>
                  <div className="text-gray-600">{selectedStock.name}</div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">{selectedStock.price.toLocaleString()}</div>
                  <div className={`text-lg ${selectedStock.priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedStock.priceChange >= 0 ? '+' : ''}{selectedStock.priceChange}%
                  </div>
                </div>
              </div>
              
              {/* Điểm số chi tiết */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h3 className="font-medium mb-3">Điểm số chi tiết</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">Dòng tiền</span>
                      <span className="font-medium">{selectedStock.flowScore}/25</span>
                    </div>
                    <RatingBar score={selectedStock.flowScore} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">Tích lũy</span>
                      <span className="font-medium">{selectedStock.accumulationScore}/25</span>
                    </div>
                    <RatingBar score={selectedStock.accumulationScore} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">Momentum</span>
                      <span className="font-medium">{selectedStock.momentumScore}/25</span>
                    </div>
                    <RatingBar score={selectedStock.momentumScore} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">Tương quan ngành</span>
                      <span className="font-medium">{selectedStock.sectorScore}/25</span>
                    </div>
                    <RatingBar score={selectedStock.sectorScore} />
                  </div>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between mb-1">
                    <span className="font-medium">TỔNG ĐIỂM</span>
                    <span className={`font-bold ${
                      selectedStock.totalScore >= 85 ? 'text-red-600' : 
                      selectedStock.totalScore >= 70 ? 'text-green-600' : 
                      selectedStock.totalScore >= 50 ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {selectedStock.totalScore}/100
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className={`h-2.5 rounded-full ${
                        selectedStock.totalScore >= 85 ? 'bg-red-600' : 
                        selectedStock.totalScore >= 70 ? 'bg-green-600' : 
                        selectedStock.totalScore >= 50 ? 'bg-blue-600' : 'bg-gray-400'
                      }`} 
                      style={{ width: `${selectedStock.totalScore}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              {/* Biểu đồ */}
              <div className="mb-4">
                <h3 className="font-medium mb-3">Biểu đồ giá 10 phiên gần nhất</h3>
                <div className="h-64 border rounded-lg p-2">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={['dataMin - 2000', 'dataMax + 2000']} />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="price" stroke="#3b82f6" activeDot={{ r: 8 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Chỉ số kỹ thuật */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h3 className="font-medium mb-3">Chỉ số kỹ thuật</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <div className="text-gray-600 text-sm">RSI (14)</div>
                    <div className={`font-medium ${
                      selectedStock.rsi > 70 ? 'text-red-600' : 
                      selectedStock.rsi > 50 ? 'text-green-600' : 
                      selectedStock.rsi > 30 ? 'text-blue-600' : 'text-red-600'
                    }`}>
                      {selectedStock.rsi}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">MACD</div>
                    <div className={`font-medium ${selectedStock.macd > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {selectedStock.macd > 0 ? '+' : ''}{selectedStock.macd}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">Khối lượng</div>
                    <div className="font-medium">
                      {(selectedStock.volume / 1000000).toFixed(1)}M
                      <span className="text-xs text-gray-500 ml-1">
                        ({Math.round(selectedStock.volume / selectedStock.avgVolume * 100)}%)
                      </span>
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">Xu hướng</div>
                    <div className="font-medium">
                      {selectedStock.trendDays > 0 ? 
                        <span className="text-green-600">Tăng {selectedStock.trendDays} phiên</span> : 
                        <span className="text-gray-600">Sideway</span>
                      }
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">Ngành</div>
                    <div className="font-medium">{selectedStock.sector}</div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">MA20</div>
                    <div className="font-medium">
                      {selectedStock.price > 82000 ? 
                        <span className="text-green-600">Trên MA20</span> : 
                        <span className="text-red-600">Dưới MA20</span>
                      }
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Nhận xét */}
              <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                <h3 className="font-medium mb-2 flex items-center">
                  <EyeIcon size={18} className="mr-2 text-blue-600" /> 
                  <span>Nhận xét & Gợi ý</span>
                </h3>
                <p className="text-gray-700">
                  {selectedStock.notes}
                </p>
                                  {selectedStock.totalScore >= 80 && (
                  <div className="mt-2 text-sm text-blue-700">
                    Đây là cổ phiếu có điểm kỹ thuật cao, nên theo dõi cơ hội mua trong 1-2 phiên tới.
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <BarChart3 size={48} className="mb-2" />
              <p>Vui lòng chọn một cổ phiếu để xem chi tiết</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StockFilterApp;