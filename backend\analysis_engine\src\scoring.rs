//! Scoring engine for stock analysis

use crate::config::{Config, ScoringWeights};
use crate::indicators::TechnicalIndicators as TechIndicators;
use crate::models::{StockData, StockScore, TechnicalIndicators, Recommendation};
use chrono::Utc;
use std::sync::Arc;

pub struct ScoringEngine {
    config: Arc<Config>,
    technical_indicators: Arc<TechIndicators>,
}

impl ScoringEngine {
    pub fn new(config: Arc<Config>, technical_indicators: Arc<TechIndicators>) -> Self {
        Self {
            config,
            technical_indicators,
        }
    }
    
    /// Calculate comprehensive stock score
    pub fn calculate_score(&self, data: &[StockData], indicators: &TechnicalIndicators) -> StockScore {
        let symbol = data.first().map(|d| d.symbol.clone()).unwrap_or_default();
        
        let money_flow_score = self.calculate_money_flow_score(data, indicators);
        let accumulation_score = self.calculate_accumulation_score(data, indicators);
        let momentum_score = self.calculate_momentum_score(indicators);
        let sector_correlation_score = self.calculate_sector_correlation_score(&symbol);
        let volume_profile_score = self.calculate_volume_profile_score(data, indicators);
        let volatility_score = self.calculate_volatility_score(data);
        
        let weights = &self.config.scoring_weights;
        let total_score = (money_flow_score * weights.money_flow)
            + (accumulation_score * weights.accumulation)
            + (momentum_score * weights.momentum)
            + (sector_correlation_score * weights.sector_correlation)
            + (volume_profile_score * weights.volume_profile)
            + (volatility_score * weights.volatility);
        
        let confidence = self.calculate_confidence(data, indicators);
        let recommendation = Recommendation::from_score(total_score);
        
        StockScore {
            symbol,
            money_flow_score,
            accumulation_score,
            momentum_score,
            sector_correlation_score,
            volume_profile_score,
            volatility_score,
            total_score: total_score.clamp(0.0, 100.0),
            updated_at: Utc::now(),
            confidence,
            recommendation,
        }
    }
    
    /// Calculate money flow score based on MFI and volume analysis
    fn calculate_money_flow_score(&self, data: &[StockData], indicators: &TechnicalIndicators) -> f64 {
        let mut score = 50.0; // Base score
        
        // Money Flow Index component
        if let Some(mfi) = indicators.volume_indicators.money_flow_index {
            score += match mfi {
                m if m > 80.0 => -20.0, // Overbought
                m if m > 60.0 => 10.0,  // Strong buying
                m if m > 40.0 => 5.0,   // Moderate buying
                m if m < 20.0 => -20.0, // Oversold
                m if m < 40.0 => -10.0, // Weak
                _ => 0.0,
            };
        }
        
        // Volume ratio component
        let volume_ratio = indicators.volume_indicators.volume_ratio;
        score += match volume_ratio {
            v if v > 2.0 => 15.0,   // High volume
            v if v > 1.5 => 10.0,   // Above average volume
            v if v > 1.0 => 5.0,    // Normal volume
            v if v < 0.5 => -10.0,  // Low volume
            _ => 0.0,
        };
        
        // Price-volume relationship
        if data.len() >= 2 {
            let current = data.last().unwrap();
            let previous = &data[data.len() - 2];
            let price_change = (current.close - previous.close) / previous.close;
            let volume_change = (current.volume as f64 - previous.volume as f64) / previous.volume as f64;
            
            // Positive correlation between price and volume is good
            if price_change > 0.0 && volume_change > 0.0 {
                score += 10.0;
            } else if price_change < 0.0 && volume_change < 0.0 {
                score += 5.0;
            } else if price_change > 0.0 && volume_change < 0.0 {
                score -= 5.0; // Price up on low volume is weak
            }
        }
        
        score.clamp(0.0, 100.0)
    }
    
    /// Calculate accumulation/distribution score
    fn calculate_accumulation_score(&self, data: &[StockData], indicators: &TechnicalIndicators) -> f64 {
        let mut score = 50.0;
        
        // On-Balance Volume trend
        let obv = indicators.volume_indicators.on_balance_volume;
        if obv > 0.0 {
            score += 15.0; // Accumulation
        } else {
            score -= 15.0; // Distribution
        }
        
        // VWAP relationship
        if let Some(current_price) = data.last().map(|d| d.close) {
            let vwap = indicators.volume_indicators.volume_weighted_average_price;
            if vwap > 0.0 {
                let vwap_ratio = current_price / vwap;
                score += match vwap_ratio {
                    r if r > 1.02 => 10.0,  // Above VWAP
                    r if r > 1.0 => 5.0,    // Slightly above VWAP
                    r if r < 0.98 => -10.0, // Below VWAP
                    r if r < 1.0 => -5.0,   // Slightly below VWAP
                    _ => 0.0,
                };
            }
        }
        
        // Price trend analysis
        if data.len() >= 20 {
            let recent_prices: Vec<f64> = data.iter().rev().take(20).map(|d| d.close).collect();
            if let Some(sma_20) = self.technical_indicators.calculate_sma(&recent_prices, 20) {
                if let Some(current_price) = data.last().map(|d| d.close) {
                    if current_price > sma_20 {
                        score += 10.0; // Above moving average
                    } else {
                        score -= 10.0; // Below moving average
                    }
                }
            }
        }
        
        score.clamp(0.0, 100.0)
    }
    
    /// Calculate momentum score based on RSI, MACD, and Stochastic
    fn calculate_momentum_score(&self, indicators: &TechnicalIndicators) -> f64 {
        let mut score = 50.0;
        
        // RSI component
        if let Some(rsi) = indicators.rsi {
            score += match rsi {
                r if r > 70.0 => -15.0, // Overbought
                r if r > 60.0 => 10.0,  // Strong momentum
                r if r > 50.0 => 5.0,   // Positive momentum
                r if r < 30.0 => -15.0, // Oversold
                r if r < 40.0 => -10.0, // Weak momentum
                _ => 0.0,
            };
        }
        
        // MACD component
        if let Some(macd) = &indicators.macd {
            // MACD line above signal line is bullish
            if macd.macd_line > macd.signal_line {
                score += 10.0;
            } else {
                score -= 10.0;
            }
            
            // Histogram growing is bullish
            if macd.histogram > 0.0 {
                score += 5.0;
            } else {
                score -= 5.0;
            }
        }
        
        // Stochastic component
        if let Some(stoch) = &indicators.stochastic {
            if stoch.k_percent > 80.0 {
                score -= 10.0; // Overbought
            } else if stoch.k_percent > 50.0 {
                score += 5.0; // Bullish
            } else if stoch.k_percent < 20.0 {
                score -= 10.0; // Oversold
            } else {
                score -= 5.0; // Bearish
            }
        }
        
        score.clamp(0.0, 100.0)
    }
    
    /// Calculate sector correlation score (placeholder implementation)
    fn calculate_sector_correlation_score(&self, _symbol: &str) -> f64 {
        // This would typically involve comparing the stock's performance
        // with its sector index and calculating correlation
        // For now, return a neutral score
        50.0
    }
    
    /// Calculate volume profile score
    fn calculate_volume_profile_score(&self, data: &[StockData], indicators: &TechnicalIndicators) -> f64 {
        let mut score = 50.0;
        
        // Volume trend analysis
        let volume_ratio = indicators.volume_indicators.volume_ratio;
        score += match volume_ratio {
            v if v > 2.0 => 20.0,   // Very high volume
            v if v > 1.5 => 15.0,   // High volume
            v if v > 1.2 => 10.0,   // Above average
            v if v > 0.8 => 0.0,    // Normal
            v if v < 0.5 => -15.0,  // Low volume
            _ => -5.0,
        };
        
        // Volume consistency
        if data.len() >= 10 {
            let recent_volumes: Vec<f64> = data.iter().rev().take(10).map(|d| d.volume as f64).collect();
            let avg_volume = recent_volumes.iter().sum::<f64>() / recent_volumes.len() as f64;
            let volume_variance = recent_volumes.iter()
                .map(|&v| (v - avg_volume).powi(2))
                .sum::<f64>() / recent_volumes.len() as f64;
            let volume_std_dev = volume_variance.sqrt();
            
            // Lower variance in volume is better for consistency
            let cv = if avg_volume > 0.0 { volume_std_dev / avg_volume } else { 1.0 };
            if cv < 0.3 {
                score += 10.0; // Consistent volume
            } else if cv > 0.7 {
                score -= 10.0; // Erratic volume
            }
        }
        
        score.clamp(0.0, 100.0)
    }
    
    /// Calculate volatility score
    fn calculate_volatility_score(&self, data: &[StockData]) -> f64 {
        if data.len() < 20 {
            return 50.0;
        }
        
        let prices: Vec<f64> = data.iter().map(|d| d.close).collect();
        let returns: Vec<f64> = prices.windows(2)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();
        
        let mean_return = returns.iter().sum::<f64>() / returns.len() as f64;
        let variance = returns.iter()
            .map(|&r| (r - mean_return).powi(2))
            .sum::<f64>() / returns.len() as f64;
        let volatility = variance.sqrt();
        
        // Lower volatility gets higher score (more stable)
        let score = match volatility {
            v if v < 0.01 => 80.0,  // Very low volatility
            v if v < 0.02 => 70.0,  // Low volatility
            v if v < 0.03 => 60.0,  // Moderate volatility
            v if v < 0.05 => 40.0,  // High volatility
            _ => 20.0,              // Very high volatility
        };
        
        score
    }
    
    /// Calculate confidence level for the score
    fn calculate_confidence(&self, data: &[StockData], indicators: &TechnicalIndicators) -> f64 {
        let mut confidence = 0.5; // Base confidence
        
        // More data points increase confidence
        let data_points = data.len() as f64;
        confidence += (data_points / 100.0).min(0.3);
        
        // Volume consistency increases confidence
        if indicators.volume_indicators.volume_ratio > 0.5 {
            confidence += 0.1;
        }
        
        // Technical indicator agreement increases confidence
        let mut indicator_signals = 0;
        let mut total_indicators = 0;
        
        if let Some(rsi) = indicators.rsi {
            total_indicators += 1;
            if rsi > 50.0 {
                indicator_signals += 1;
            }
        }
        
        if let Some(macd) = &indicators.macd {
            total_indicators += 1;
            if macd.macd_line > macd.signal_line {
                indicator_signals += 1;
            }
        }
        
        if total_indicators > 0 {
            let agreement = indicator_signals as f64 / total_indicators as f64;
            confidence += (agreement - 0.5).abs() * 0.2;
        }
        
        confidence.clamp(0.0, 1.0)
    }
}
