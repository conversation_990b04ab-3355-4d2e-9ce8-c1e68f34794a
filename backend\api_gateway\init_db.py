"""
Database initialization script for StockFilterX
"""
import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from config import settings
from database import Base, database
from db_models import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_database_if_not_exists():
    """Create database if it doesn't exist"""
    try:
        # Connect to postgres database to create our database
        postgres_url = settings.DATABASE_URL.replace(f"/{settings.DB_NAME}", "/postgres")
        engine = create_async_engine(postgres_url)
        
        async with engine.connect() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": settings.DB_NAME}
            )
            
            if not result.fetchone():
                # Create database
                await conn.execute(text("COMMIT"))  # End any transaction
                await conn.execute(text(f"CREATE DATABASE {settings.DB_NAME}"))
                logger.info(f"Database {settings.DB_NAME} created successfully")
            else:
                logger.info(f"Database {settings.DB_NAME} already exists")
        
        await engine.dispose()
        
    except Exception as e:
        logger.error(f"Error creating database: {str(e)}")
        raise


async def init_database():
    """Initialize database with tables and extensions"""
    try:
        logger.info("Initializing database...")
        
        # Create database if it doesn't exist
        await create_database_if_not_exists()
        
        # Connect to our database
        engine = create_async_engine(settings.DATABASE_URL)
        
        async with engine.begin() as conn:
            # Create TimescaleDB extension
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;"))
            logger.info("TimescaleDB extension created")
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("All tables created")
            
            # Create hypertables for time-series data
            try:
                await conn.execute(
                    text("SELECT create_hypertable('stock_data', 'timestamp', if_not_exists => TRUE);")
                )
                logger.info("stock_data hypertable created")
            except Exception as e:
                logger.warning(f"Could not create stock_data hypertable: {str(e)}")
            
            try:
                await conn.execute(
                    text("SELECT create_hypertable('stock_scores', 'updated_at', if_not_exists => TRUE);")
                )
                logger.info("stock_scores hypertable created")
            except Exception as e:
                logger.warning(f"Could not create stock_scores hypertable: {str(e)}")
            
            try:
                await conn.execute(
                    text("SELECT create_hypertable('api_usage', 'timestamp', if_not_exists => TRUE);")
                )
                logger.info("api_usage hypertable created")
            except Exception as e:
                logger.warning(f"Could not create api_usage hypertable: {str(e)}")
        
        await engine.dispose()
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise


async def create_admin_user():
    """Create default admin user"""
    try:
        from auth import get_password_hash
        
        # Connect to database
        await database.connect()
        
        # Check if admin user exists
        admin_exists = await database.fetch_one(
            "SELECT id FROM users WHERE username = 'admin'"
        )
        
        if not admin_exists:
            # Create admin user
            hashed_password = get_password_hash("admin123")  # Change this in production!
            
            await database.execute(
                """
                INSERT INTO users (username, email, full_name, hashed_password, role, subscription_status)
                VALUES (:username, :email, :full_name, :hashed_password, :role, :subscription_status)
                """,
                {
                    "username": "admin",
                    "email": "<EMAIL>",
                    "full_name": "System Administrator",
                    "hashed_password": hashed_password,
                    "role": "admin",
                    "subscription_status": "active"
                }
            )
            
            logger.info("Admin user created successfully")
            logger.warning("Default admin password is 'admin123' - CHANGE THIS IN PRODUCTION!")
        else:
            logger.info("Admin user already exists")
        
        await database.disconnect()
        
    except Exception as e:
        logger.error(f"Error creating admin user: {str(e)}")
        raise


async def seed_sample_data():
    """Seed database with sample data for development"""
    try:
        await database.connect()
        
        # Sample stock symbols
        symbols = ["VIC", "VHM", "VNM", "BID", "CTG", "TCB", "HPG", "MSN", "SAB", "GAS"]
        
        # Insert sample stock data
        from datetime import datetime, timedelta
        import random
        
        base_time = datetime.utcnow() - timedelta(days=30)
        
        for symbol in symbols:
            base_price = random.uniform(10, 100)
            
            for i in range(30):  # 30 days of data
                timestamp = base_time + timedelta(days=i)
                
                # Generate realistic OHLCV data
                open_price = base_price * (1 + random.uniform(-0.05, 0.05))
                high_price = open_price * (1 + random.uniform(0, 0.1))
                low_price = open_price * (1 - random.uniform(0, 0.1))
                close_price = open_price * (1 + random.uniform(-0.08, 0.08))
                volume = random.randint(100000, 10000000)
                
                await database.execute(
                    """
                    INSERT INTO stock_data (symbol, timestamp, open, high, low, close, volume)
                    VALUES (:symbol, :timestamp, :open, :high, :low, :close, :volume)
                    ON CONFLICT DO NOTHING
                    """,
                    {
                        "symbol": symbol,
                        "timestamp": timestamp,
                        "open": open_price,
                        "high": high_price,
                        "low": low_price,
                        "close": close_price,
                        "volume": volume
                    }
                )
                
                # Generate sample scores
                await database.execute(
                    """
                    INSERT INTO stock_scores (symbol, money_flow_score, accumulation_score, 
                                            momentum_score, sector_correlation_score, total_score, updated_at)
                    VALUES (:symbol, :mf_score, :acc_score, :mom_score, :sec_score, :total_score, :updated_at)
                    ON CONFLICT DO NOTHING
                    """,
                    {
                        "symbol": symbol,
                        "mf_score": random.uniform(0, 100),
                        "acc_score": random.uniform(0, 100),
                        "mom_score": random.uniform(0, 100),
                        "sec_score": random.uniform(0, 100),
                        "total_score": random.uniform(0, 100),
                        "updated_at": timestamp
                    }
                )
        
        await database.disconnect()
        logger.info("Sample data seeded successfully")
        
    except Exception as e:
        logger.error(f"Error seeding sample data: {str(e)}")
        raise


async def main():
    """Main initialization function"""
    try:
        await init_database()
        await create_admin_user()
        
        # Only seed sample data in development
        if settings.ENVIRONMENT == "development":
            await seed_sample_data()
        
        logger.info("Database setup completed successfully!")
        
    except Exception as e:
        logger.error(f"Database setup failed: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
