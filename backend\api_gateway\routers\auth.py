"""
Authentication router for StockFilterX API
"""
import logging
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from auth import (
    create_access_token, create_refresh_token, get_current_active_user,
    get_password_hash, verify_password, authenticate_user, decode_token
)
from models import User, UserCreate, UserUpdate, Token
from database import get_db
from db_models import UserDB, UserSessionDB
from config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/register", response_model=User)
async def register(user: UserCreate, db: AsyncSession = Depends(get_db)):
    """Register a new user"""
    try:
        # Check if email already exists
        result = await db.execute(select(UserDB).where(UserDB.email == user.email))
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email đã được đăng ký"
            )

        # Check if username already exists
        result = await db.execute(select(UserDB).where(UserDB.username == user.username))
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tên đăng nhập đã tồn tại"
            )

        # Create new user
        hashed_password = get_password_hash(user.password)
        db_user = UserDB(
            email=user.email,
            username=user.username,
            full_name=user.full_name,
            hashed_password=hashed_password,
            role="basic",
            subscription_status="trial"
        )

        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)

        logger.info(f"New user registered: {user.username}")

        # Convert to Pydantic model
        return User(
            id=db_user.id,
            email=db_user.email,
            username=db_user.username,
            full_name=db_user.full_name,
            role=db_user.role,
            subscription_status=db_user.subscription_status,
            disabled=db_user.disabled,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at,
            last_login=db_user.last_login,
            subscription_expires_at=db_user.subscription_expires_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Đăng ký thất bại"
        )

@router.post("/token", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """Login and get access token"""
    try:
        # Authenticate user (can use email or username)
        user = await authenticate_user(db, form_data.username, form_data.password)
        if not user:
            # Try with email if username failed
            result = await db.execute(select(UserDB).where(UserDB.email == form_data.username))
            email_user = result.scalar_one_or_none()
            if email_user:
                user = await authenticate_user(db, email_user.username, form_data.password)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Email hoặc mật khẩu không chính xác",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if user.disabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tài khoản đã bị vô hiệu hóa"
            )

        # Create tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id},
            expires_delta=access_token_expires
        )

        refresh_token = create_refresh_token(
            data={"sub": user.username, "user_id": user.id}
        )

        # Update last login
        user.last_login = datetime.utcnow()
        await db.commit()

        logger.info(f"User logged in: {user.username}")

        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Đăng nhập thất bại"
        )

@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        payload = decode_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        username = payload.get("sub")
        user_id = payload.get("user_id")

        if not username or not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        # Get user from database
        result = await db.execute(select(UserDB).where(UserDB.id == user_id))
        user = result.scalar_one_or_none()

        if not user or user.disabled:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or disabled"
            )

        # Create new tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id},
            expires_delta=access_token_expires
        )

        new_refresh_token = create_refresh_token(
            data={"sub": user.username, "user_id": user.id}
        )

        return Token(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not refresh token"
        )


@router.get("/me", response_model=User)
async def read_users_me(current_user: UserDB = Depends(get_current_active_user)):
    """Get current user information"""
    return User(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        full_name=current_user.full_name,
        role=current_user.role,
        subscription_status=current_user.subscription_status,
        disabled=current_user.disabled,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        last_login=current_user.last_login,
        subscription_expires_at=current_user.subscription_expires_at
    )


@router.put("/me", response_model=User)
async def update_user(
    user_update: UserUpdate,
    current_user: UserDB = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information"""
    try:
        # Update user fields
        if user_update.email:
            # Check if email is already taken by another user
            result = await db.execute(
                select(UserDB).where(UserDB.email == user_update.email, UserDB.id != current_user.id)
            )
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email đã được sử dụng"
                )
            current_user.email = user_update.email

        if user_update.full_name is not None:
            current_user.full_name = user_update.full_name

        current_user.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(current_user)

        logger.info(f"User updated: {current_user.username}")

        return User(
            id=current_user.id,
            email=current_user.email,
            username=current_user.username,
            full_name=current_user.full_name,
            role=current_user.role,
            subscription_status=current_user.subscription_status,
            disabled=current_user.disabled,
            created_at=current_user.created_at,
            updated_at=current_user.updated_at,
            last_login=current_user.last_login,
            subscription_expires_at=current_user.subscription_expires_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User update error: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Cập nhật thông tin thất bại"
        )


@router.post("/logout")
async def logout(current_user: UserDB = Depends(get_current_active_user)):
    """Logout user (client should discard tokens)"""
    logger.info(f"User logged out: {current_user.username}")
    return {"message": "Đăng xuất thành công"}