{"name": "stockfilterx-mobile", "version": "1.0.0", "description": "StockFilterX Mobile Application - Intelligent Stock Filtering System", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "expo": "~49.0.15", "expo-auth-session": "~5.0.2", "expo-crypto": "~12.4.1", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-linear-gradient": "~12.3.0", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-web-browser": "~12.3.2", "react": "18.2.0", "react-native": "0.72.6", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.11.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.0.3", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "victory-native": "^36.8.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "^0.72.6", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "jest-expo": "~49.0.0", "typescript": "^5.1.3"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}