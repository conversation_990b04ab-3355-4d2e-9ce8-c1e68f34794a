# Database Configuration
DATABASE_URL=postgresql://stockfilterx:stockfilterx@localhost:5432/stockfilterx

# Redis Configuration
REDIS_URL=redis://localhost:6379

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672

# Analysis Configuration
ANALYSIS_INTERVAL=60
BATCH_SIZE=100
MAX_CONCURRENT_ANALYSIS=10

# Logging
LOG_LEVEL=info
RUST_LOG=info

# Environment
ENVIRONMENT=development

# Metrics
METRICS_PORT=9090
