import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { TextInput, Button, Text, useTheme } from 'react-native-paper';
import { useMutation } from '@tanstack/react-query';

interface AuthData {
  email: string;
  password: string;
  username?: string;
  full_name?: string;
}

const API_URL = 'http://localhost:8000/api/v1';

const AuthScreen = () => {
  const theme = useTheme();
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState<AuthData>({
    email: '',
    password: '',
    username: '',
    full_name: ''
  });

  const loginMutation = useMutation({
    mutationFn: async (data: AuthData) => {
      const response = await fetch(`${API_URL}/auth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `username=${data.email}&password=${data.password}`,
      });
      if (!response.ok) {
        throw new Error('Đăng nhập thất bại');
      }
      return response.json();
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (data: AuthData) => {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error('Đăng ký thất bại');
      }
      return response.json();
    },
  });

  const handleSubmit = () => {
    if (isLogin) {
      loginMutation.mutate(formData);
    } else {
      registerMutation.mutate(formData);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {isLogin ? 'Đăng nhập' : 'Đăng ký'}
      </Text>

      <TextInput
        label="Email"
        value={formData.email}
        onChangeText={(text) => setFormData({ ...formData, email: text })}
        style={styles.input}
      />

      {!isLogin && (
        <>
          <TextInput
            label="Tên đăng nhập"
            value={formData.username}
            onChangeText={(text) => setFormData({ ...formData, username: text })}
            style={styles.input}
          />
          <TextInput
            label="Họ và tên"
            value={formData.full_name}
            onChangeText={(text) => setFormData({ ...formData, full_name: text })}
            style={styles.input}
          />
        </>
      )}

      <TextInput
        label="Mật khẩu"
        value={formData.password}
        onChangeText={(text) => setFormData({ ...formData, password: text })}
        secureTextEntry
        style={styles.input}
      />

      <Button
        mode="contained"
        onPress={handleSubmit}
        loading={loginMutation.isPending || registerMutation.isPending}
        style={styles.button}
      >
        {isLogin ? 'Đăng nhập' : 'Đăng ký'}
      </Button>

      <Button
        mode="text"
        onPress={() => setIsLogin(!isLogin)}
        style={styles.switchButton}
      >
        {isLogin ? 'Chưa có tài khoản? Đăng ký ngay' : 'Đã có tài khoản? Đăng nhập'}
      </Button>

      {(loginMutation.isError || registerMutation.isError) && (
        <Text style={styles.errorText}>
          {loginMutation.error?.message || registerMutation.error?.message}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
  },
  switchButton: {
    marginTop: 16,
  },
  errorText: {
    color: 'red',
    marginTop: 16,
    textAlign: 'center',
  },
});

export default AuthScreen;