import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Switch, Button, List, useTheme, Portal, Dialog, TextInput } from 'react-native-paper';
import { useQuery, useMutation } from '@tanstack/react-query';

interface AlertSettings {
  notification_channels: string[];
  price_alerts: Record<string, Array<{
    price: number;
    condition: string;
    active: boolean;
  }>>;
  score_alerts: Record<string, Array<{
    score_type: string;
    threshold: number;
    active: boolean;
  }>>;
}

const API_URL = 'http://localhost:8000/api/v1';

const SettingsScreen = () => {
  const theme = useTheme();
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [password, setPassword] = useState({ current: '', new: '', confirm: '' });

  const { data: settings, isLoading } = useQuery<AlertSettings>({
    queryKey: ['alert-settings'],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/alerts/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      if (!response.ok) throw new Error('Không thể tải cài đặt');
      return response.json();
    },
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: Partial<AlertSettings>) => {
      const response = await fetch(`${API_URL}/alerts/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(newSettings),
      });
      if (!response.ok) throw new Error('Không thể cập nhật cài đặt');
      return response.json();
    },
  });

  const changePasswordMutation = useMutation({
    mutationFn: async (passwords: typeof password) => {
      const response = await fetch(`${API_URL}/auth/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          current_password: passwords.current,
          new_password: passwords.new,
        }),
      });
      if (!response.ok) throw new Error('Không thể đổi mật khẩu');
      return response.json();
    },
  });

  const handleChangePassword = () => {
    if (password.new !== password.confirm) {
      alert('Mật khẩu mới không khớp');
      return;
    }
    changePasswordMutation.mutate(password);
    setShowChangePassword(false);
    setPassword({ current: '', new: '', confirm: '' });
  };

  const handleNotificationChange = (type: 'email' | 'push', value: boolean) => {
    const newChannels = [...(settings?.notification_channels || [])];
    if (value) {
      newChannels.push(type);
    } else {
      const index = newChannels.indexOf(type);
      if (index > -1) newChannels.splice(index, 1);
    }
    updateSettingsMutation.mutate({ notification_channels: newChannels });
  };

  return (
    <ScrollView style={styles.container}>
      <List.Section>
        <List.Subheader>Thông báo</List.Subheader>
        <List.Item
          title="Email"
          right={() => (
            <Switch
              value={emailNotifications}
              onValueChange={(value) => {
                setEmailNotifications(value);
                handleNotificationChange('email', value);
              }}
            />
          )}
        />
        <List.Item
          title="Push Notification"
          right={() => (
            <Switch
              value={pushNotifications}
              onValueChange={(value) => {
                setPushNotifications(value);
                handleNotificationChange('push', value);
              }}
            />
          )}
        />
      </List.Section>

      <List.Section>
        <List.Subheader>Tài khoản</List.Subheader>
        <List.Item
          title="Đổi mật khẩu"
          onPress={() => setShowChangePassword(true)}
          right={props => <List.Icon {...props} icon="chevron-right" />}
        />
      </List.Section>

      <Portal>
        <Dialog
          visible={showChangePassword}
          onDismiss={() => setShowChangePassword(false)}
        >
          <Dialog.Title>Đổi mật khẩu</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Mật khẩu hiện tại"
              value={password.current}
              onChangeText={(text) => setPassword({ ...password, current: text })}
              secureTextEntry
              style={styles.input}
            />
            <TextInput
              label="Mật khẩu mới"
              value={password.new}
              onChangeText={(text) => setPassword({ ...password, new: text })}
              secureTextEntry
              style={styles.input}
            />
            <TextInput
              label="Xác nhận mật khẩu mới"
              value={password.confirm}
              onChangeText={(text) => setPassword({ ...password, confirm: text })}
              secureTextEntry
              style={styles.input}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowChangePassword(false)}>Hủy</Button>
            <Button onPress={handleChangePassword}>Xác nhận</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  input: {
    marginBottom: 16,
  },
});

export default SettingsScreen;