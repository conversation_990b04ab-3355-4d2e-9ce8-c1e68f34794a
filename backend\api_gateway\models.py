from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    disabled: bool = False
    created_at: datetime
    watchlists: List[str] = []
    alert_settings: dict = {}

    class Config:
        orm_mode = True

class WatchlistBase(BaseModel):
    name: str
    symbols: List[str] = []

class WatchlistCreate(WatchlistBase):
    pass

class Watchlist(WatchlistBase):
    id: int
    user_id: int
    created_at: datetime

    class Config:
        orm_mode = True

class AlertSettings(BaseModel):
    user_id: int
    price_alerts: dict = {}
    score_alerts: dict = {}
    notification_channels: List[str] = ["email"]

    class Config:
        orm_mode = True