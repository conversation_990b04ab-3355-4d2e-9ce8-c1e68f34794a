"""
Pydantic models for StockFilterX API
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr, Field, validator
from enum import Enum

# Enums
class UserRole(str, Enum):
    ADMIN = "admin"
    PREMIUM = "premium"
    BASIC = "basic"

class SubscriptionStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    TRIAL = "trial"
    EXPIRED = "expired"

class AlertType(str, Enum):
    PRICE = "price"
    SCORE = "score"
    VOLUME = "volume"
    TECHNICAL = "technical"

class NotificationChannel(str, Enum):
    EMAIL = "email"
    PUSH = "push"
    SMS = "sms"
    WEBHOOK = "webhook"

# User models
class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = Field(None, max_length=100)

    @validator('username')
    def validate_username(cls, v):
        if not v.isalnum():
            raise ValueError('Username must be alphanumeric')
        return v.lower()

class UserCreate(UserBase):
    password: str = Field(..., min_length=8)

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)

class User(UserBase):
    id: int
    role: UserRole = UserRole.BASIC
    subscription_status: SubscriptionStatus = SubscriptionStatus.TRIAL
    disabled: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    subscription_expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Authentication models
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    username: Optional[str] = None
    user_id: Optional[int] = None

# Stock models
class StockData(BaseModel):
    symbol: str
    timestamp: datetime
    open: float = Field(..., gt=0)
    high: float = Field(..., gt=0)
    low: float = Field(..., gt=0)
    close: float = Field(..., gt=0)
    volume: int = Field(..., ge=0)

class StockScore(BaseModel):
    symbol: str
    money_flow_score: float = Field(..., ge=0, le=100)
    accumulation_score: float = Field(..., ge=0, le=100)
    momentum_score: float = Field(..., ge=0, le=100)
    sector_correlation_score: float = Field(..., ge=0, le=100)
    total_score: float = Field(..., ge=0, le=100)
    updated_at: datetime

class StockAnalysis(BaseModel):
    symbol: str
    current_price: float
    price_change: float
    price_change_percent: float
    volume: int
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None
    scores: StockScore
    recommendation: str
    confidence: float = Field(..., ge=0, le=1)

# Watchlist models
class WatchlistBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    symbols: List[str] = Field(default_factory=list)

    @validator('symbols')
    def validate_symbols(cls, v):
        # Remove duplicates and validate format
        unique_symbols = list(set(symbol.upper() for symbol in v))
        for symbol in unique_symbols:
            if not symbol.isalpha() or len(symbol) > 10:
                raise ValueError(f'Invalid symbol format: {symbol}')
        return unique_symbols

class WatchlistCreate(WatchlistBase):
    pass

class WatchlistUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    symbols: Optional[List[str]] = None

class Watchlist(WatchlistBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_public: bool = False

    class Config:
        from_attributes = True

# Alert models
class AlertCondition(BaseModel):
    field: str  # price, score, volume, etc.
    operator: str  # >, <, >=, <=, ==
    value: float

class AlertBase(BaseModel):
    symbol: str
    alert_type: AlertType
    condition: AlertCondition
    message: Optional[str] = None
    is_active: bool = True

class AlertCreate(AlertBase):
    pass

class AlertUpdate(BaseModel):
    condition: Optional[AlertCondition] = None
    message: Optional[str] = None
    is_active: Optional[bool] = None

class Alert(AlertBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    triggered_at: Optional[datetime] = None
    trigger_count: int = 0

    class Config:
        from_attributes = True

class AlertSettings(BaseModel):
    user_id: int
    notification_channels: List[NotificationChannel] = [NotificationChannel.EMAIL]
    email_notifications: bool = True
    push_notifications: bool = True
    sms_notifications: bool = False
    webhook_url: Optional[str] = None
    quiet_hours_start: Optional[str] = None  # HH:MM format
    quiet_hours_end: Optional[str] = None    # HH:MM format

    class Config:
        from_attributes = True

# Notification models
class NotificationBase(BaseModel):
    title: str = Field(..., max_length=200)
    message: str = Field(..., max_length=1000)
    notification_type: str
    channel: NotificationChannel

class NotificationCreate(NotificationBase):
    user_id: int

class Notification(NotificationBase):
    id: int
    user_id: int
    created_at: datetime
    read_at: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

# API Response models
class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int

class HealthCheck(BaseModel):
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, str]

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None